const { PrismaClient } = require('@prisma/client');

async function enableAllSystemsForResidentialProperties() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== ENABLING ALL SYSTEMS FOR RESIDENTIAL PROPERTIES ===');
    
    // Get all residential properties
    const residentialProperties = await prisma.property.findMany({
      where: {
        type: 'RESIDENTIAL',
        isActive: true
      },
      select: {
        id: true,
        name: true,
        type: true,
      }
    });
    
    console.log(`Found ${residentialProperties.length} residential properties:`);
    residentialProperties.forEach(prop => {
      console.log(`- ${prop.name} (${prop.id})`);
    });
    
    // Define all available systems
    const allSystems = [
      { systemType: 'WATER', displayName: 'Water Management', displayOrder: 1 },
      { systemType: 'ELECTRICITY', displayName: 'Electricity Management', displayOrder: 2 },
      { systemType: 'SECURITY', displayName: 'Security Management', displayOrder: 3 },
      { systemType: 'INTERNET', displayName: 'Internet Management', displayOrder: 4 },
      { systemType: 'OTT', displayName: 'OTT Services', displayOrder: 5 },
      { systemType: 'MAINTENANCE', displayName: 'Maintenance Management', displayOrder: 6 },
    ];
    
    console.log('\n=== CHECKING CURRENT SYSTEM CONFIGURATIONS ===');
    
    let totalConfigsCreated = 0;
    let totalConfigsUpdated = 0;
    let totalStatusesCreated = 0;
    
    for (const property of residentialProperties) {
      console.log(`\n📋 Processing: ${property.name}`);
      
      // Check existing system configurations
      const existingConfigs = await prisma.propertySystemConfig.findMany({
        where: { propertyId: property.id },
        select: { systemType: true, isEnabled: true }
      });
      
      console.log(`  Current configs: ${existingConfigs.length}`);
      existingConfigs.forEach(config => {
        console.log(`    - ${config.systemType}: ${config.isEnabled ? 'ENABLED' : 'DISABLED'}`);
      });
      
      // Check existing system statuses
      const existingStatuses = await prisma.systemStatus.findMany({
        where: { propertyId: property.id },
        select: { systemType: true, status: true }
      });
      
      console.log(`  Current statuses: ${existingStatuses.length}`);
      existingStatuses.forEach(status => {
        console.log(`    - ${status.systemType}: ${status.status}`);
      });
      
      // Create or update system configurations
      for (const system of allSystems) {
        const existingConfig = existingConfigs.find(c => c.systemType === system.systemType);
        
        if (existingConfig) {
          // Update existing config to ensure it's enabled
          if (!existingConfig.isEnabled) {
            await prisma.propertySystemConfig.updateMany({
              where: {
                propertyId: property.id,
                systemType: system.systemType
              },
              data: {
                isEnabled: true,
                displayName: system.displayName,
                displayOrder: system.displayOrder
              }
            });
            console.log(`    ✅ Updated ${system.systemType} config (enabled)`);
            totalConfigsUpdated++;
          } else {
            console.log(`    ⏭️  ${system.systemType} config already enabled`);
          }
        } else {
          // Create new config
          await prisma.propertySystemConfig.create({
            data: {
              propertyId: property.id,
              systemType: system.systemType,
              isEnabled: true,
              displayName: system.displayName,
              displayOrder: system.displayOrder,
              configuration: {}
            }
          });
          console.log(`    ✅ Created ${system.systemType} config`);
          totalConfigsCreated++;
        }
        
        // Create or update system status
        const existingStatus = existingStatuses.find(s => s.systemType === system.systemType);
        
        if (!existingStatus) {
          await prisma.systemStatus.create({
            data: {
              propertyId: property.id,
              systemType: system.systemType,
              status: 'OPERATIONAL',
              description: `${system.systemType.toLowerCase()} system operational`,
              healthScore: Math.floor(Math.random() * 20) + 80, // 80-100
            }
          });
          console.log(`    ✅ Created ${system.systemType} status`);
          totalStatusesCreated++;
        } else {
          console.log(`    ⏭️  ${system.systemType} status already exists`);
        }
      }
    }
    
    console.log('\n=== VERIFICATION ===');
    
    // Verify all residential properties now have all systems enabled
    for (const property of residentialProperties) {
      const configs = await prisma.propertySystemConfig.findMany({
        where: { 
          propertyId: property.id,
          isEnabled: true 
        },
        orderBy: { displayOrder: 'asc' }
      });
      
      const statuses = await prisma.systemStatus.findMany({
        where: { propertyId: property.id },
        orderBy: { systemType: 'asc' }
      });
      
      console.log(`\n${property.name}:`);
      console.log(`  ✅ Enabled systems: ${configs.length}/6`);
      configs.forEach(config => {
        console.log(`    - ${config.systemType} (${config.displayName})`);
      });
      console.log(`  ✅ System statuses: ${statuses.length}/6`);
      statuses.forEach(status => {
        console.log(`    - ${status.systemType}: ${status.status} (${status.healthScore}%)`);
      });
    }
    
    console.log('\n=== SUMMARY ===');
    console.log(`✅ Processed ${residentialProperties.length} residential properties`);
    console.log(`✅ Created ${totalConfigsCreated} new system configurations`);
    console.log(`✅ Updated ${totalConfigsUpdated} existing system configurations`);
    console.log(`✅ Created ${totalStatusesCreated} new system statuses`);
    console.log(`🎉 All residential properties now have all 6 systems enabled!`);
    
    // Final verification count
    const totalEnabledSystems = await prisma.propertySystemConfig.count({
      where: {
        property: { type: 'RESIDENTIAL' },
        isEnabled: true
      }
    });
    
    const expectedTotal = residentialProperties.length * 6;
    console.log(`\n📊 Final verification: ${totalEnabledSystems}/${expectedTotal} systems enabled`);
    
    if (totalEnabledSystems === expectedTotal) {
      console.log('🎉 SUCCESS: All systems enabled for all residential properties!');
    } else {
      console.log('⚠️  WARNING: Some systems may not be enabled properly');
    }
    
  } catch (error) {
    console.error('❌ Error enabling systems:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the enable operation
enableAllSystemsForResidentialProperties()
  .then(() => {
    console.log('✅ Enable operation completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Enable operation failed:', error);
    process.exit(1);
  });

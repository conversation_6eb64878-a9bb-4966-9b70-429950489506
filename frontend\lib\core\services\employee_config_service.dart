
import '../constants/api_constants.dart';
import '../network/api_client.dart';
import '../utils/logger.dart';
import '../utils/api_response.dart';

class EmployeeConfigService {
  final ApiClient _apiClient;

  EmployeeConfigService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();

  // Department Management
  Future<ApiResponse<List<EmployeeDepartment>>> getDepartments() async {
    try {
      final response = await _apiClient.get(ApiConstants.employeeDepartments);

      if (response.data['success'] == true) {
        final departments = (response.data['data'] as List<dynamic>)
            .map((dept) => EmployeeDepartment.fromJson(dept))
            .toList();
        
        return ApiResponse<List<EmployeeDepartment>>(
          success: true,
          data: departments,
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch departments');
      }
    } catch (e) {
      Logger.error('Error fetching departments: $e');
      return ApiResponse.error('Failed to fetch departments');
    }
  }

  Future<ApiResponse<EmployeeDepartment>> getDepartmentById(String departmentId) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.employeeDepartments}/$departmentId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<EmployeeDepartment>(
          success: true,
          data: EmployeeDepartment.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch department');
      }
    } catch (e) {
      Logger.error('Error fetching department: $e');
      return ApiResponse.error('Failed to fetch department');
    }
  }

  Future<ApiResponse<EmployeeDepartment>> createDepartment({
    required String name,
    required String code,
    String? description,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.employeeDepartments,
        data: {
          'name': name,
          'code': code,
          'description': description,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<EmployeeDepartment>(
          success: true,
          data: EmployeeDepartment.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to create department');
      }
    } catch (e) {
      Logger.error('Error creating department: $e');
      return ApiResponse.error('Failed to create department');
    }
  }

  Future<ApiResponse<EmployeeDepartment>> updateDepartment({
    required String departmentId,
    String? name,
    String? code,
    String? description,
  }) async {
    try {
      final response = await _apiClient.put(
        '${ApiConstants.employeeDepartments}/$departmentId',
        data: {
          if (name != null) 'name': name,
          if (code != null) 'code': code,
          if (description != null) 'description': description,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<EmployeeDepartment>(
          success: true,
          data: EmployeeDepartment.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update department');
      }
    } catch (e) {
      Logger.error('Error updating department: $e');
      return ApiResponse.error('Failed to update department');
    }
  }

  Future<ApiResponse<void>> deleteDepartment(String departmentId) async {
    try {
      final response = await _apiClient.delete(
        '${ApiConstants.employeeDepartments}/$departmentId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to delete department');
      }
    } catch (e) {
      Logger.error('Error deleting department: $e');
      return ApiResponse.error('Failed to delete department');
    }
  }

  // Employee Status Management
  Future<ApiResponse<List<EmployeeStatus>>> getEmployeeStatuses() async {
    try {
      final response = await _apiClient.get(ApiConstants.employeeStatuses);

      if (response.data['success'] == true) {
        final statuses = (response.data['data'] as List<dynamic>)
            .map((status) => EmployeeStatus.fromJson(status))
            .toList();
        
        return ApiResponse<List<EmployeeStatus>>(
          success: true,
          data: statuses,
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch employee statuses');
      }
    } catch (e) {
      Logger.error('Error fetching employee statuses: $e');
      return ApiResponse.error('Failed to fetch employee statuses');
    }
  }

  Future<ApiResponse<EmployeeStatus>> createEmployeeStatus({
    required String name,
    required String code,
    String? description,
    bool? isActive,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.employeeStatuses,
        data: {
          'name': name,
          'code': code,
          'description': description,
          'isActive': isActive,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<EmployeeStatus>(
          success: true,
          data: EmployeeStatus.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to create employee status');
      }
    } catch (e) {
      Logger.error('Error creating employee status: $e');
      return ApiResponse.error('Failed to create employee status');
    }
  }

  Future<ApiResponse<EmployeeStatus>> updateEmployeeStatus({
    required String statusId,
    String? name,
    String? code,
    String? description,
    bool? isActive,
  }) async {
    try {
      final response = await _apiClient.put(
        '${ApiConstants.employeeStatuses}/$statusId',
        data: {
          if (name != null) 'name': name,
          if (code != null) 'code': code,
          if (description != null) 'description': description,
          if (isActive != null) 'isActive': isActive,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<EmployeeStatus>(
          success: true,
          data: EmployeeStatus.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update employee status');
      }
    } catch (e) {
      Logger.error('Error updating employee status: $e');
      return ApiResponse.error('Failed to update employee status');
    }
  }

  Future<ApiResponse<void>> deleteEmployeeStatus(String statusId) async {
    try {
      final response = await _apiClient.delete(
        '${ApiConstants.employeeStatuses}/$statusId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to delete employee status');
      }
    } catch (e) {
      Logger.error('Error deleting employee status: $e');
      return ApiResponse.error('Failed to delete employee status');
    }
  }
}

// Employee Department Model
class EmployeeDepartment {
  final String id;
  final String name;
  final String? description;
  final String code;
  final int employeeCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  EmployeeDepartment({
    required this.id,
    required this.name,
    this.description,
    required this.code,
    required this.employeeCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeDepartment.fromJson(Map<String, dynamic> json) {
    return EmployeeDepartment(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      code: json['code'],
      employeeCount: json['employeeCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'code': code,
      'employeeCount': employeeCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

// Employee Status Model
class EmployeeStatus {
  final String id;
  final String name;
  final String? description;
  final String code;
  final bool isActive;
  final int employeeCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  EmployeeStatus({
    required this.id,
    required this.name,
    this.description,
    required this.code,
    required this.isActive,
    required this.employeeCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeStatus.fromJson(Map<String, dynamic> json) {
    return EmployeeStatus(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      code: json['code'],
      isActive: json['isActive'] ?? true,
      employeeCount: json['employeeCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'code': code,
      'isActive': isActive,
      'employeeCount': employeeCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}


import '../constants/api_constants.dart';
import '../network/api_client.dart';
import '../utils/logger.dart';
import '../utils/api_response.dart';

class AnalyticsService {
  final ApiClient _apiClient;

  AnalyticsService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();

  // Maintenance Analytics
  Future<ApiResponse<MaintenanceAnalytics>> getMaintenanceAnalytics({
    String? propertyId,
    String? startDate,
    String? endDate,
    String? timeRange,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;
      if (timeRange != null) queryParams['timeRange'] = timeRange;

      final response = await _apiClient.get(
        ApiConstants.maintenanceAnalytics,
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        return ApiResponse<MaintenanceAnalytics>(
          success: true,
          data: MaintenanceAnalytics.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch maintenance analytics');
      }
    } catch (e) {
      Logger.error('Error fetching maintenance analytics: $e');
      return ApiResponse.error('Failed to fetch maintenance analytics');
    }
  }

  // Security Analytics
  Future<ApiResponse<SecurityAnalytics>> getSecurityAnalytics({
    String? propertyId,
    String? startDate,
    String? endDate,
    String? timeRange,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;
      if (timeRange != null) queryParams['timeRange'] = timeRange;

      final response = await _apiClient.get(
        ApiConstants.securityAnalytics,
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        return ApiResponse<SecurityAnalytics>(
          success: true,
          data: SecurityAnalytics.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch security analytics');
      }
    } catch (e) {
      Logger.error('Error fetching security analytics: $e');
      return ApiResponse.error('Failed to fetch security analytics');
    }
  }
}

// Maintenance Analytics Models
class MaintenanceAnalytics {
  final AnalyticsOverview overview;
  final List<TrendData> trends;
  final List<DepartmentStats> departments;
  final PerformanceMetrics performance;
  final TimeRange timeRange;

  MaintenanceAnalytics({
    required this.overview,
    required this.trends,
    required this.departments,
    required this.performance,
    required this.timeRange,
  });

  factory MaintenanceAnalytics.fromJson(Map<String, dynamic> json) {
    return MaintenanceAnalytics(
      overview: AnalyticsOverview.fromJson(json['overview']),
      trends: (json['trends'] as List<dynamic>)
          .map((t) => TrendData.fromJson(t))
          .toList(),
      departments: (json['departments'] as List<dynamic>)
          .map((d) => DepartmentStats.fromJson(d))
          .toList(),
      performance: PerformanceMetrics.fromJson(json['performance']),
      timeRange: TimeRange.fromJson(json['timeRange']),
    );
  }
}

class AnalyticsOverview {
  final int totalIssues;
  final int resolvedIssues;
  final int openIssues;
  final int inProgressIssues;
  final double resolutionRate;
  final double avgResolutionTime;

  AnalyticsOverview({
    required this.totalIssues,
    required this.resolvedIssues,
    required this.openIssues,
    required this.inProgressIssues,
    required this.resolutionRate,
    required this.avgResolutionTime,
  });

  factory AnalyticsOverview.fromJson(Map<String, dynamic> json) {
    return AnalyticsOverview(
      totalIssues: json['totalIssues'] ?? 0,
      resolvedIssues: json['resolvedIssues'] ?? 0,
      openIssues: json['openIssues'] ?? 0,
      inProgressIssues: json['inProgressIssues'] ?? 0,
      resolutionRate: (json['resolutionRate'] ?? 0).toDouble(),
      avgResolutionTime: (json['avgResolutionTime'] ?? 0).toDouble(),
    );
  }
}

class TrendData {
  final String date;
  final int totalIssues;
  final int resolvedIssues;
  final int openIssues;

  TrendData({
    required this.date,
    required this.totalIssues,
    required this.resolvedIssues,
    required this.openIssues,
  });

  factory TrendData.fromJson(Map<String, dynamic> json) {
    return TrendData(
      date: json['date'],
      totalIssues: json['totalIssues'] ?? 0,
      resolvedIssues: json['resolvedIssues'] ?? 0,
      openIssues: json['openIssues'] ?? 0,
    );
  }
}

class DepartmentStats {
  final String name;
  final int total;
  final int resolved;
  final int open;
  final int inProgress;
  final double resolutionRate;

  DepartmentStats({
    required this.name,
    required this.total,
    required this.resolved,
    required this.open,
    required this.inProgress,
    required this.resolutionRate,
  });

  factory DepartmentStats.fromJson(Map<String, dynamic> json) {
    return DepartmentStats(
      name: json['name'],
      total: json['total'] ?? 0,
      resolved: json['resolved'] ?? 0,
      open: json['open'] ?? 0,
      inProgress: json['inProgress'] ?? 0,
      resolutionRate: (json['resolutionRate'] ?? 0).toDouble(),
    );
  }
}

class PerformanceMetrics {
  final double avgResolutionTime;
  final double resolutionRate;
  final double customerSatisfaction;
  final double escalationRate;

  PerformanceMetrics({
    required this.avgResolutionTime,
    required this.resolutionRate,
    required this.customerSatisfaction,
    required this.escalationRate,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) {
    return PerformanceMetrics(
      avgResolutionTime: (json['avgResolutionTime'] ?? 0).toDouble(),
      resolutionRate: (json['resolutionRate'] ?? 0).toDouble(),
      customerSatisfaction: (json['customerSatisfaction'] ?? 0).toDouble(),
      escalationRate: (json['escalationRate'] ?? 0).toDouble(),
    );
  }
}

class TimeRange {
  final String startDate;
  final String endDate;
  final String period;

  TimeRange({
    required this.startDate,
    required this.endDate,
    required this.period,
  });

  factory TimeRange.fromJson(Map<String, dynamic> json) {
    return TimeRange(
      startDate: json['startDate'],
      endDate: json['endDate'],
      period: json['period'],
    );
  }
}

// Security Analytics Models
class SecurityAnalytics {
  final SecurityOverview overview;
  final List<TrendData> trends;
  final SystemHealth systemHealth;
  final TimeRange timeRange;

  SecurityAnalytics({
    required this.overview,
    required this.trends,
    required this.systemHealth,
    required this.timeRange,
  });

  factory SecurityAnalytics.fromJson(Map<String, dynamic> json) {
    return SecurityAnalytics(
      overview: SecurityOverview.fromJson(json['overview']),
      trends: (json['trends'] as List<dynamic>)
          .map((t) => TrendData.fromJson(t))
          .toList(),
      systemHealth: SystemHealth.fromJson(json['systemHealth']),
      timeRange: TimeRange.fromJson(json['timeRange']),
    );
  }
}

class SecurityOverview {
  final int totalIncidents;
  final int resolvedIncidents;
  final int activeIncidents;
  final double resolutionRate;

  SecurityOverview({
    required this.totalIncidents,
    required this.resolvedIncidents,
    required this.activeIncidents,
    required this.resolutionRate,
  });

  factory SecurityOverview.fromJson(Map<String, dynamic> json) {
    return SecurityOverview(
      totalIncidents: json['totalIncidents'] ?? 0,
      resolvedIncidents: json['resolvedIncidents'] ?? 0,
      activeIncidents: json['activeIncidents'] ?? 0,
      resolutionRate: (json['resolutionRate'] ?? 0).toDouble(),
    );
  }
}

class SystemHealth {
  final int operational;
  final int warning;
  final int critical;
  final int total;

  SystemHealth({
    required this.operational,
    required this.warning,
    required this.critical,
    required this.total,
  });

  factory SystemHealth.fromJson(Map<String, dynamic> json) {
    return SystemHealth(
      operational: json['operational'] ?? 0,
      warning: json['warning'] ?? 0,
      critical: json['critical'] ?? 0,
      total: json['total'] ?? 0,
    );
  }
}

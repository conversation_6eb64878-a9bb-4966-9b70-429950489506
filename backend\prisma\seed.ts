import { PrismaClient, UserRole, PropertyType, SystemType, OfficeType, MaintenancePriority, MaintenanceStatus, RecurrenceType } from '@prisma/client';
import { AuthService } from '../src/lib/auth';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default admin user
  const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';
  const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';

  const hashedPassword = await AuthService.hashPassword(adminPassword);

  const admin = await prisma.user.upsert({
    where: { email: adminEmail },
    update: {},
    create: {
      name: adminName,
      email: adminEmail,
      phone: '+919999999999',
      password: hashedPassword,
      role: UserRole.SUPER_ADMIN,
      isActive: true,
      timezone: 'Asia/Kolkata',
      language: 'en',
    },
  });

  console.log('✅ Created admin user:', admin.email);

  // Create sample properties
  const properties = [
    {
      name: 'Jubilee Hills Residence',
      type: PropertyType.RESIDENTIAL,
      address: 'Road No. 36, Jubilee Hills, Hyderabad, Telangana 500033',
      description: 'Luxury residential property in prime Jubilee Hills location',
      latitude: 17.4239,
      longitude: 78.4738,
      images: [],
    },
    {
      name: 'Banjara Hills Office Complex',
      type: PropertyType.OFFICE,
      address: 'Road No. 12, Banjara Hills, Hyderabad, Telangana 500034',
      description: 'Modern office complex with state-of-the-art facilities',
      latitude: 17.4126,
      longitude: 78.4482,
      images: [],
    },
    {
      name: 'Gandipet Construction Site',
      type: PropertyType.CONSTRUCTION,
      address: 'Gandipet, Hyderabad, Telangana 500075',
      description: 'Ongoing residential construction project',
      latitude: 17.3616,
      longitude: 78.2747,
      images: [],
    },
  ];

  const createdProperties = [];
  for (const propertyData of properties) {
    // Check if property already exists by name
    const existingProperty = await prisma.property.findFirst({
      where: { name: propertyData.name },
    });

    const property = existingProperty || await prisma.property.create({
      data: propertyData,
    });

    // Create system statuses for each property
    const systemTypes: SystemType[] = ['WATER', 'ELECTRICITY', 'SECURITY', 'INTERNET', 'OTT', 'MAINTENANCE'];
    
    for (const systemType of systemTypes) {
      await prisma.systemStatus.upsert({
        where: {
          propertyId_systemType: {
            propertyId: property.id,
            systemType,
          },
        },
        update: {},
        create: {
          propertyId: property.id,
          systemType,
          status: 'OPERATIONAL',
          description: `${systemType.toLowerCase()} system operational`,
          healthScore: Math.floor(Math.random() * 20) + 80, // 80-100
        },
      });
    }

    createdProperties.push(property);
    console.log('✅ Created property:', property.name);
  }

  // Create sample users with different roles
  const users = [
    {
      name: 'Property Manager',
      email: '<EMAIL>',
      phone: '+919999999998',
      role: UserRole.PROPERTY_MANAGER,
      assignedProperties: [createdProperties[0].id, createdProperties[1].id],
    },
    {
      name: 'Office Manager',
      email: '<EMAIL>',
      phone: '+919999999997',
      role: UserRole.OFFICE_MANAGER,
      assignedProperties: [],
    },
    {
      name: 'Security Personnel',
      email: '<EMAIL>',
      phone: '+919999999996',
      role: UserRole.SECURITY_PERSONNEL,
      assignedProperties: [createdProperties[0].id],
    },
    {
      name: 'Maintenance Staff',
      email: '<EMAIL>',
      phone: '+919999999995',
      role: UserRole.MAINTENANCE_STAFF,
      assignedProperties: [createdProperties[0].id, createdProperties[1].id],
    },
    {
      name: 'Construction Supervisor',
      email: '<EMAIL>',
      phone: '+919999999994',
      role: UserRole.CONSTRUCTION_SUPERVISOR,
      assignedProperties: [createdProperties[2].id],
    },
  ];

  for (const userData of users) {
    const hashedUserPassword = await AuthService.hashPassword('password123');
    
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        password: hashedUserPassword,
        role: userData.role,
        isActive: true,
        timezone: 'Asia/Kolkata',
        language: 'en',
      },
    });

    // Assign properties to user
    for (const propertyId of userData.assignedProperties) {
      await prisma.userProperty.upsert({
        where: {
          userId_propertyId: {
            userId: user.id,
            propertyId,
          },
        },
        update: {},
        create: {
          userId: user.id,
          propertyId,
        },
      });
    }

    console.log('✅ Created user:', user.email);
  }

  // Create sample offices
  const offices = [
    {
      name: 'Main Office - Hyderabad',
      type: OfficeType.OFFICE,
      address: 'HITEC City, Hyderabad, Telangana 500081',
      latitude: 17.4435,
      longitude: 78.3772,
      workingHours: {
        monday: { start: '09:00', end: '18:00' },
        tuesday: { start: '09:00', end: '18:00' },
        wednesday: { start: '09:00', end: '18:00' },
        thursday: { start: '09:00', end: '18:00' },
        friday: { start: '09:00', end: '18:00' },
        saturday: { start: '09:00', end: '14:00' },
        sunday: { closed: true },
      },
    },
    {
      name: 'Gandipet Construction Site',
      type: OfficeType.CONSTRUCTION_SITE,
      address: 'Gandipet, Hyderabad, Telangana 500075',
      latitude: 17.3616,
      longitude: 78.2747,
      workingHours: {
        monday: { start: '08:00', end: '17:00' },
        tuesday: { start: '08:00', end: '17:00' },
        wednesday: { start: '08:00', end: '17:00' },
        thursday: { start: '08:00', end: '17:00' },
        friday: { start: '08:00', end: '17:00' },
        saturday: { start: '08:00', end: '17:00' },
        sunday: { closed: true },
      },
    },
  ];

  const createdOffices = [];
  for (const officeData of offices) {
    // Check if office already exists by name
    const existingOffice = await prisma.office.findFirst({
      where: { name: officeData.name },
    });

    const office = existingOffice || await prisma.office.create({
      data: officeData,
    });

    createdOffices.push(office);
    console.log('✅ Created office:', office.name);
  }

  // Create employee departments first
  const employeeDepartments = [
    { name: 'Electricity', description: 'Electrical systems and power management', code: 'ELEC' },
    { name: 'Water', description: 'Water supply and plumbing systems', code: 'WATER' },
    { name: 'Security', description: 'Security systems and surveillance', code: 'SEC' },
    { name: 'Internet', description: 'Network and internet connectivity', code: 'NET' },
    { name: 'Generator', description: 'Backup power generation systems', code: 'GEN' },
    { name: 'CCTV', description: 'Closed-circuit television monitoring', code: 'CCTV' },
    { name: 'Maintenance', description: 'General maintenance and repairs', code: 'MAINT' },
    { name: 'OTTs', description: 'Over-the-top streaming services', code: 'OTT' },
  ];

  const createdEmployeeDepartments = [];
  for (const deptData of employeeDepartments) {
    const department = await prisma.employeeDepartment.upsert({
      where: { name: deptData.name },
      update: {},
      create: deptData,
    });
    createdEmployeeDepartments.push(department);
    console.log('✅ Created employee department:', department.name);
  }

  // Create employee statuses
  const employeeStatuses = [
    { name: 'Active', description: 'Currently employed and working', code: 'ACTIVE', isActive: true },
    { name: 'On Leave', description: 'Temporarily on leave', code: 'LEAVE', isActive: true },
    { name: 'Terminated', description: 'Employment terminated', code: 'TERM', isActive: false },
    { name: 'Resigned', description: 'Resigned from position', code: 'RESIGN', isActive: false },
  ];

  const createdEmployeeStatuses = [];
  for (const statusData of employeeStatuses) {
    const status = await prisma.employeeStatus.upsert({
      where: { name: statusData.name },
      update: {},
      create: statusData,
    });
    createdEmployeeStatuses.push(status);
    console.log('✅ Created employee status:', status.name);
  }

  // Create sample employees with more realistic data
  const employees = [
    {
      officeId: createdOffices[0].id,
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+919999999993',
      employeeId: 'EMP001',
      designation: 'Electrician',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Electricity')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-01-15'),
      isActive: true,
    },
    {
      officeId: createdOffices[0].id,
      name: 'Jane Wilson',
      email: '<EMAIL>',
      phone: '+919999999992',
      employeeId: 'EMP002',
      designation: 'Security Guard',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Security')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-02-01'),
      isActive: true,
    },
    {
      officeId: createdOffices[1].id,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+919999999991',
      employeeId: 'EMP003',
      designation: 'Maintenance Supervisor',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Maintenance')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-03-01'),
      isActive: true,
    },
    {
      officeId: createdOffices[0].id,
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      phone: '+919999999990',
      employeeId: 'EMP004',
      designation: 'Water System Technician',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Water')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2022-11-10'),
      isActive: true,
    },
    {
      officeId: createdOffices[1].id,
      name: 'David Brown',
      email: '<EMAIL>',
      phone: '+919999999989',
      employeeId: 'EMP005',
      designation: 'Security Chief',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Security')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2022-08-20'),
      isActive: true,
    },
    {
      officeId: createdOffices[0].id,
      name: 'Lisa Anderson',
      email: '<EMAIL>',
      phone: '+919999999988',
      employeeId: 'EMP006',
      designation: 'Network Administrator',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Internet')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-04-12'),
      isActive: true,
    },
    {
      officeId: createdOffices[1].id,
      name: 'Robert Taylor',
      email: '<EMAIL>',
      phone: '+919999999987',
      employeeId: 'EMP007',
      designation: 'Plumber',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Water')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-06-05'),
      isActive: true,
    },
    {
      officeId: createdOffices[1].id,
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+919999999986',
      employeeId: 'EMP008',
      designation: 'Generator Technician',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Generator')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-07-18'),
      isActive: true,
    },
    {
      officeId: createdOffices[0].id,
      name: 'James Miller',
      email: '<EMAIL>',
      phone: '+919999999985',
      employeeId: 'EMP009',
      designation: 'CCTV Operator',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'CCTV')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-09-22'),
      isActive: true,
    },
    {
      officeId: createdOffices[0].id,
      name: 'Maria Garcia',
      email: '<EMAIL>',
      phone: '+919999999984',
      employeeId: 'EMP010',
      designation: 'OTT Support Specialist',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'OTTs')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2023-10-15'),
      isActive: true,
    },
    {
      officeId: createdOffices[1].id,
      name: 'Kevin Lee',
      email: '<EMAIL>',
      phone: '+919999999983',
      employeeId: 'EMP011',
      designation: 'Maintenance Technician',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Maintenance')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'Active')!.id,
      joinDate: new Date('2024-01-08'),
      isActive: true,
    },
    {
      officeId: createdOffices[0].id,
      name: 'Anna Thompson',
      email: '<EMAIL>',
      phone: '+919999999982',
      employeeId: 'EMP012',
      designation: 'Facility Coordinator',
      departmentId: createdEmployeeDepartments.find(d => d.name === 'Maintenance')!.id,
      statusId: createdEmployeeStatuses.find(s => s.name === 'On Leave')!.id,
      joinDate: new Date('2024-02-14'),
      isActive: false, // On leave
    },
  ];

  for (const employeeData of employees) {
    const employee = await prisma.employee.upsert({
      where: { employeeId: employeeData.employeeId },
      update: {},
      create: employeeData,
    });

    console.log('✅ Created employee:', employee.name);
  }

  // Create sample alerts with more variety for better dashboard appearance
  const alerts = [
    {
      propertyId: createdProperties[0].id,
      title: 'Water Tank Level Low',
      message: 'Water tank level is below 20%. Immediate attention required.',
      severity: 'CRITICAL' as const,
      category: 'WATER',
      status: 'OPEN' as const,
      metadata: {
        tankLevel: 18,
        threshold: 20,
        location: 'Rooftop Tank A',
      },
    },
    {
      propertyId: createdProperties[1].id,
      title: 'Security Camera Offline',
      message: 'Camera #3 in parking area is not responding.',
      severity: 'HIGH' as const,
      category: 'SECURITY',
      status: 'OPEN' as const,
      metadata: {
        cameraId: 'CAM003',
        location: 'Parking Area',
      },
    },
    {
      propertyId: createdProperties[0].id,
      title: 'Power Outage Detected',
      message: 'Main power supply interrupted. Generator auto-started.',
      severity: 'CRITICAL' as const,
      category: 'ELECTRICITY',
      status: 'RESOLVED' as const,
      metadata: {
        duration: '15 minutes',
        generatorStatus: 'Running',
        affectedAreas: 'Building A, Floors 1-3',
      },
    },
    {
      propertyId: createdProperties[2].id,
      title: 'Internet Speed Below Threshold',
      message: 'Internet speed dropped to 25 Mbps, below contracted 50 Mbps.',
      severity: 'MEDIUM' as const,
      category: 'INTERNET',
      status: 'OPEN' as const,
      metadata: {
        currentSpeed: 25,
        contractedSpeed: 50,
        provider: 'ACT Fibernet',
      },
    },
    {
      propertyId: createdProperties[1].id,
      title: 'Generator Fuel Low',
      message: 'Generator fuel level at 15%. Refueling required soon.',
      severity: 'HIGH' as const,
      category: 'GENERATOR',
      status: 'ACKNOWLEDGED' as const,
      metadata: {
        fuelLevel: 15,
        estimatedRuntime: '4 hours',
        lastRefill: '2025-05-10',
      },
    },
    {
      propertyId: createdProperties[0].id,
      title: 'Unauthorized Access Attempt',
      message: 'Failed access attempt detected at main entrance.',
      severity: 'CRITICAL' as const,
      category: 'SECURITY',
      status: 'ACKNOWLEDGED' as const,
      metadata: {
        location: 'Main Entrance',
        time: '2025-05-18 23:45:00',
        attempts: 3,
      },
    },
    {
      propertyId: createdProperties[2].id,
      title: 'HVAC System Maintenance Due',
      message: 'Quarterly HVAC maintenance is overdue by 5 days.',
      severity: 'LOW' as const,
      category: 'MAINTENANCE',
      status: 'OPEN' as const,
      metadata: {
        lastMaintenance: '2025-02-15',
        dueDate: '2025-05-15',
        overdueDays: 5,
      },
    },
    {
      propertyId: createdProperties[1].id,
      title: 'OTT Service Expiring',
      message: 'Netflix subscription expires in 3 days.',
      severity: 'LOW' as const,
      category: 'OTT',
      status: 'OPEN' as const,
      metadata: {
        service: 'Netflix',
        expiryDate: '2025-05-25',
        daysRemaining: 3,
      },
    },
    {
      propertyId: createdProperties[0].id,
      title: 'Elevator Maintenance Required',
      message: 'Elevator #1 requires monthly safety inspection.',
      severity: 'MEDIUM' as const,
      category: 'MAINTENANCE',
      status: 'OPEN' as const,
      metadata: {
        elevatorId: 'ELV001',
        lastInspection: '2025-04-20',
        nextDue: '2025-05-20',
      },
    },
    {
      propertyId: createdProperties[1].id,
      title: 'Fire Alarm System Test',
      message: 'Monthly fire alarm system test completed successfully.',
      severity: 'LOW' as const,
      category: 'SECURITY',
      status: 'RESOLVED' as const,
      metadata: {
        testDate: '2025-05-15',
        allZonesTested: true,
        issuesFound: 0,
      },
    },
    {
      propertyId: createdProperties[2].id,
      title: 'Water Pressure Low',
      message: 'Water pressure in Building B is below normal levels.',
      severity: 'MEDIUM' as const,
      category: 'WATER',
      status: 'ACKNOWLEDGED' as const,
      metadata: {
        currentPressure: 25,
        normalPressure: 40,
        affectedFloors: '3-5',
      },
    },
    {
      propertyId: createdProperties[0].id,
      title: 'Backup Generator Test',
      message: 'Weekly backup generator test scheduled for tomorrow.',
      severity: 'LOW' as const,
      category: 'GENERATOR',
      status: 'OPEN' as const,
      metadata: {
        scheduledDate: '2025-05-22',
        duration: '30 minutes',
        lastTest: '2025-05-15',
      },
    },
  ];

  for (const alertData of alerts) {
    const alert = await prisma.alert.create({
      data: alertData,
    });

    console.log('✅ Created alert:', alert.title);
  }

  // Get created users for maintenance issue assignments
  const manager = await prisma.user.findFirst({ where: { email: '<EMAIL>' } });
  const securityPersonnel = await prisma.user.findFirst({ where: { email: '<EMAIL>' } });
  const maintenanceStaff = await prisma.user.findFirst({ where: { email: '<EMAIL>' } });

  // Create sample departments
  const departments = [
    {
      name: 'Electricity',
      description: 'Electrical systems and power management',
    },
    {
      name: 'Water',
      description: 'Water supply and plumbing systems',
    },
    {
      name: 'Security',
      description: 'Security systems and surveillance',
    },
    {
      name: 'Internet',
      description: 'Network and internet connectivity',
    },
    {
      name: 'Generator',
      description: 'Backup power generation systems',
    },
    {
      name: 'CCTV',
      description: 'Closed-circuit television monitoring',
    },
    {
      name: 'Maintenance',
      description: 'General maintenance and repairs',
    },
    {
      name: 'OTTs',
      description: 'Over-the-top streaming services',
    },
  ];

  const createdDepartments = [];
  for (const deptData of departments) {
    const department = await prisma.department.upsert({
      where: { name: deptData.name },
      update: {},
      create: deptData,
    });
    createdDepartments.push(department);
    console.log('✅ Created department:', department.name);
  }

  // Create sample maintenance issues with more realistic data
  const maintenanceIssues = [
    {
      title: 'Invertor Maintenance',
      description: 'Monthly maintenance check for invertor system. Check battery levels, clean terminals, and test backup functionality.',
      departmentId: createdDepartments.find(d => d.name === 'Electricity')!.id,
      propertyId: createdProperties[0].id,
      priority: 'LOW' as const,
      status: 'RESOLVED' as const,
      startDate: new Date('2025-05-01'),
      expectedEndDate: new Date('2025-05-31'),
      actualEndDate: new Date('2025-05-15'),
      reportedBy: admin.id,
      isRecurring: true,
      recurrenceType: 'MONTHLY' as const,
      frequency: 'Every month',
      nextDueDate: new Date('2025-06-01'),
      remarks: 'Completed successfully. Battery levels optimal.',
    },
    {
      title: 'CCTV Annual Maintenance',
      description: 'Annual maintenance and inspection of CCTV systems. Clean camera lenses, check recording quality, test night vision, and verify storage capacity.',
      departmentId: createdDepartments.find(d => d.name === 'Security')!.id,
      propertyId: createdProperties[0].id,
      priority: 'MEDIUM' as const,
      status: 'IN_PROGRESS' as const,
      startDate: new Date('2025-06-01'),
      expectedEndDate: new Date('2025-06-30'),
      reportedBy: admin.id,
      assignedTo: manager?.id,
      isRecurring: true,
      recurrenceType: 'YEARLY' as const,
      frequency: 'Once a year',
      remarks: 'Camera 3 needs lens replacement. Storage at 85% capacity.',
    },
    {
      title: 'ACs - Annual Cleaning and Maintenance',
      description: 'Annual cleaning and maintenance of air conditioning units',
      departmentId: createdDepartments.find(d => d.name === 'Electricity')!.id,
      propertyId: createdProperties[0].id,
      priority: 'LOW' as const,
      status: 'CLOSED' as const,
      startDate: new Date('2025-05-01'),
      expectedEndDate: new Date('2025-05-31'),
      reportedBy: admin.id,
      isRecurring: true,
      recurrenceType: 'YEARLY' as const,
      frequency: 'Annually',
    },
    {
      title: 'Generator - Annual Preventive Maintenance',
      description: 'Annual preventive maintenance for backup generator',
      departmentId: createdDepartments.find(d => d.name === 'Generator')!.id,
      propertyId: createdProperties[0].id,
      priority: 'MEDIUM' as const,
      status: 'RESOLVED' as const,
      startDate: new Date('2024-11-30'),
      expectedEndDate: new Date('2024-12-31'),
      reportedBy: admin.id,
      isRecurring: true,
      recurrenceType: 'YEARLY' as const,
      frequency: 'Annual preventive maintenance',
    },
    {
      title: 'Water Tank Automation - Dry Run on 24th May 2025',
      description: 'Scheduled dry run test for water tank automation system',
      departmentId: createdDepartments.find(d => d.name === 'Water')!.id,
      propertyId: createdProperties[0].id,
      priority: 'HIGH' as const,
      status: 'RESOLVED' as const,
      startDate: new Date('2025-05-24'),
      expectedEndDate: new Date('2025-05-24'),
      reportedBy: admin.id,
      isRecurring: false,
    },
    {
      title: 'Water Tank - Semi Annual Cleaning',
      description: 'Semi-annual cleaning and maintenance of water tanks',
      departmentId: createdDepartments.find(d => d.name === 'Water')!.id,
      propertyId: createdProperties[0].id,
      priority: 'MEDIUM' as const,
      status: 'RESOLVED' as const,
      startDate: new Date('2025-05-16'),
      expectedEndDate: new Date('2025-05-31'),
      reportedBy: admin.id,
      isRecurring: true,
      recurrenceType: 'QUARTERLY' as const,
      frequency: 'Every 6 months',
    },
    {
      title: 'Lighting - All external lighting in and around the House are functional',
      description: 'Check and ensure all external lighting systems are working properly',
      departmentId: createdDepartments.find(d => d.name === 'Electricity')!.id,
      propertyId: createdProperties[0].id,
      priority: 'LOW' as const,
      status: 'CLOSED' as const,
      startDate: new Date('2025-05-01'),
      expectedEndDate: new Date('2025-05-31'),
      reportedBy: admin.id,
      isRecurring: true,
      recurrenceType: 'YEARLY' as const,
      frequency: 'Annual inspection',
    },
    {
      title: 'Generator Automation',
      description: 'Automated generator system monitoring and maintenance. Install smart monitoring sensors and configure automatic start/stop functionality.',
      departmentId: createdDepartments.find(d => d.name === 'Generator')!.id,
      propertyId: createdProperties[0].id,
      priority: 'HIGH' as const,
      status: 'OPEN' as const,
      startDate: new Date('2025-05-12'),
      expectedEndDate: new Date('2025-06-12'),
      reportedBy: admin.id,
      assignedTo: securityPersonnel?.id,
      isRecurring: false,
      remarks: 'Urgent: Manual monitoring is time-consuming. Need automation ASAP.',
    },
    {
      title: 'Water Pump Motor Replacement',
      description: 'Replace faulty water pump motor in Building A. Motor is making unusual noise and efficiency has decreased.',
      departmentId: createdDepartments.find(d => d.name === 'Water')!.id,
      propertyId: createdProperties[0].id,
      priority: 'HIGH' as const,
      status: 'OPEN' as const,
      startDate: new Date('2025-05-20'),
      expectedEndDate: new Date('2025-05-25'),
      reportedBy: maintenanceStaff?.id || admin.id,
      isRecurring: false,
      remarks: 'Residents complaining about low water pressure. Immediate action required.',
    },
    {
      title: 'Internet Speed Optimization',
      description: 'Optimize internet connectivity and upgrade bandwidth. Current speed is below contracted levels.',
      departmentId: createdDepartments.find(d => d.name === 'Internet')!.id,
      propertyId: createdProperties[1].id,
      priority: 'MEDIUM' as const,
      status: 'IN_PROGRESS' as const,
      startDate: new Date('2025-05-10'),
      expectedEndDate: new Date('2025-05-30'),
      reportedBy: admin.id,
      assignedTo: manager?.id,
      isRecurring: false,
      remarks: 'ISP technician scheduled for site visit on May 22nd.',
    },
    {
      title: 'Security System Upgrade',
      description: 'Upgrade security system with new access control cards and biometric scanners for main entrance.',
      departmentId: createdDepartments.find(d => d.name === 'Security')!.id,
      propertyId: createdProperties[1].id,
      priority: 'MEDIUM' as const,
      status: 'OPEN' as const,
      startDate: new Date('2025-06-01'),
      expectedEndDate: new Date('2025-06-15'),
      reportedBy: securityPersonnel?.id || admin.id,
      isRecurring: false,
      remarks: 'Budget approved. Vendor selection in progress.',
    },
    {
      title: 'OTT Subscription Renewal',
      description: 'Renew Netflix, Amazon Prime, and Disney+ subscriptions for common areas and guest rooms.',
      departmentId: createdDepartments.find(d => d.name === 'OTTs')!.id,
      propertyId: createdProperties[0].id,
      priority: 'LOW' as const,
      status: 'RESOLVED' as const,
      startDate: new Date('2025-05-01'),
      expectedEndDate: new Date('2025-05-05'),
      actualEndDate: new Date('2025-05-03'),
      reportedBy: admin.id,
      isRecurring: true,
      recurrenceType: 'YEARLY' as const,
      frequency: 'Annual renewal',
      remarks: 'All subscriptions renewed successfully. Valid until May 2026.',
    },
    {
      title: 'Elevator Maintenance',
      description: 'Quarterly maintenance of elevator systems. Check cables, motors, safety systems, and emergency features.',
      departmentId: createdDepartments.find(d => d.name === 'Maintenance')!.id,
      propertyId: createdProperties[2].id,
      priority: 'HIGH' as const,
      status: 'IN_PROGRESS' as const,
      startDate: new Date('2025-05-15'),
      expectedEndDate: new Date('2025-05-20'),
      reportedBy: maintenanceStaff?.id || admin.id,
      assignedTo: manager?.id,
      isRecurring: true,
      recurrenceType: 'QUARTERLY' as const,
      frequency: 'Every 3 months',
      remarks: 'Elevator 2 has minor door alignment issue. Technician on-site.',
    },
    {
      title: 'Fire Safety System Check',
      description: 'Monthly fire safety system inspection. Test smoke detectors, fire alarms, sprinkler systems, and emergency exits.',
      departmentId: createdDepartments.find(d => d.name === 'Security')!.id,
      propertyId: createdProperties[2].id,
      priority: 'HIGH' as const,
      status: 'RESOLVED' as const,
      startDate: new Date('2025-05-01'),
      expectedEndDate: new Date('2025-05-05'),
      actualEndDate: new Date('2025-05-04'),
      reportedBy: securityPersonnel?.id || admin.id,
      isRecurring: true,
      recurrenceType: 'MONTHLY' as const,
      frequency: 'Monthly inspection',
      nextDueDate: new Date('2025-06-01'),
      remarks: 'All systems operational. Fire extinguishers refilled.',
    },
  ];

  for (const issueData of maintenanceIssues) {
    const issue = await prisma.maintenanceIssue.create({
      data: issueData,
    });
    console.log('✅ Created maintenance issue:', issue.title);
  }

  // Create sample maintenance functions
  const maintenanceFunctions = [
    {
      name: 'Attendance - Office',
      subFunction: 'Staff Presence',
      departmentId: createdDepartments.find(d => d.name === 'Maintenance')!.id,
      propertyId: createdProperties[0].id,
      input: 'Check-in/check-out logs',
      process: 'Compare vs shift schedule',
      output: 'Present/Absent',
      thresholdLimits: 'Max 1 absence/week',
      responsibleAgent: 'Office Incharge',
    },
    {
      name: 'Attendance - Site',
      subFunction: 'Security Presence',
      departmentId: createdDepartments.find(d => d.name === 'Security')!.id,
      propertyId: createdProperties[0].id,
      input: 'Log entry/exit',
      process: 'Auto-capture + manual verification',
      output: 'On Duty/Off Duty',
      thresholdLimits: 'Min 2 guards always',
      responsibleAgent: 'Site Security Incharge',
    },
    {
      name: 'CCTV',
      subFunction: 'Feed Uptime',
      departmentId: createdDepartments.find(d => d.name === 'CCTV')!.id,
      propertyId: createdProperties[0].id,
      input: 'IP stream status',
      process: 'Ping every hour',
      output: 'Online/Offline',
      thresholdLimits: 'Max downtime: 2 hrs',
      responsibleAgent: 'CCTV Vendor',
    },
    {
      name: 'CCTV',
      subFunction: 'Footage Backup',
      departmentId: createdDepartments.find(d => d.name === 'CCTV')!.id,
      propertyId: createdProperties[0].id,
      input: 'Drive backup logs',
      process: 'Check backup script',
      output: 'Success/Failure',
      thresholdLimits: 'Daily backup required',
      responsibleAgent: 'IT Assistant',
    },
    {
      name: 'Electricity',
      subFunction: 'Consumption Tracker',
      departmentId: createdDepartments.find(d => d.name === 'Electricity')!.id,
      propertyId: createdProperties[0].id,
      input: 'Energy meter data (kWh)',
      process: 'Calculate daily/monthly usage',
      output: 'kWh per day/month',
      thresholdLimits: 'Max daily: 10 kWh',
      responsibleAgent: 'Electrician',
    },
    {
      name: 'Electricity',
      subFunction: 'Load Monitoring',
      departmentId: createdDepartments.find(d => d.name === 'Electricity')!.id,
      propertyId: createdProperties[0].id,
      input: 'Device-level wattage',
      process: 'Aggregation from devices',
      output: 'Real-time load in watts',
      thresholdLimits: 'Max: 3000W/appliance',
      responsibleAgent: 'Technical Officer',
    },
    {
      name: 'Electricity',
      subFunction: 'Power Bill Status',
      departmentId: createdDepartments.find(d => d.name === 'Electricity')!.id,
      propertyId: createdProperties[0].id,
      input: 'Billing data',
      process: 'Read bill from dashboard',
      output: 'Paid/Unpaid & Due Date',
      thresholdLimits: 'Min 3 days before due',
      responsibleAgent: 'Admin',
    },
    {
      name: 'Generator',
      subFunction: 'Fuel Level Monitoring',
      departmentId: createdDepartments.find(d => d.name === 'Generator')!.id,
      propertyId: createdProperties[0].id,
      input: 'Manual entry or sensor',
      process: 'Log/check daily',
      output: 'Fuel % or litres',
      thresholdLimits: 'Min: 20%',
      responsibleAgent: 'Site Security Incharge',
    },
    {
      name: 'Generator',
      subFunction: 'Service Schedule',
      departmentId: createdDepartments.find(d => d.name === 'Generator')!.id,
      propertyId: createdProperties[0].id,
      input: 'Last service date',
      process: 'Service calendar check',
      output: 'Service due or not',
      thresholdLimits: 'Every 3 months',
      responsibleAgent: 'Generator Vendor/AMC',
    },
    {
      name: 'Generator',
      subFunction: 'Usage Hours',
      departmentId: createdDepartments.find(d => d.name === 'Generator')!.id,
      propertyId: createdProperties[0].id,
      input: 'Runtime logs',
      process: 'Log generator run hours',
      output: 'Daily usage in hours',
      thresholdLimits: 'Max: 2 hrs/day',
      responsibleAgent: 'Site Engineer',
    },
    {
      name: 'Internet',
      subFunction: 'Connectivity Check',
      departmentId: createdDepartments.find(d => d.name === 'Internet')!.id,
      propertyId: createdProperties[0].id,
      input: 'Ping status',
      process: 'Scheduled speed/ping test',
      output: 'Connected/Disconnected',
      thresholdLimits: 'Max downtime: 10 mins/day',
      responsibleAgent: 'Network Admin',
    },
    {
      name: 'Internet',
      subFunction: 'Speed Monitoring',
      departmentId: createdDepartments.find(d => d.name === 'Internet')!.id,
      propertyId: createdProperties[0].id,
      input: 'Speedtest result',
      process: 'Log speed hourly',
      output: 'Mbps download/upload',
      thresholdLimits: 'Min: 50 Mbps',
      responsibleAgent: 'Internet Vendor',
    },
    {
      name: 'Maintenance',
      subFunction: 'Issue Tracker',
      departmentId: createdDepartments.find(d => d.name === 'Maintenance')!.id,
      propertyId: createdProperties[0].id,
      input: 'Reported issue + photos',
      process: 'Log & assign issue',
      output: 'Open/In Progress/Resolved',
      thresholdLimits: 'Resolve within 3 days',
      responsibleAgent: 'Admin',
    },
    {
      name: 'Maintenance',
      subFunction: 'Scheduled Maintenance',
      departmentId: createdDepartments.find(d => d.name === 'Maintenance')!.id,
      propertyId: createdProperties[0].id,
      input: 'Last service record',
      process: 'Check calendar',
      output: 'Due/Not Due',
      thresholdLimits: 'Max interval: 60 days',
      responsibleAgent: 'Maintenance Supervisor',
    },
    {
      name: 'OTTs',
      subFunction: 'Subscription Tracker',
      departmentId: createdDepartments.find(d => d.name === 'OTTs')!.id,
      propertyId: createdProperties[0].id,
      input: 'Renewal date, cost',
      process: 'Check plan status monthly',
      output: 'Active/Inactive',
      thresholdLimits: 'Renewal 2 days before expiry',
      responsibleAgent: 'Admin',
    },
    {
      name: 'Security',
      subFunction: 'Alarm System Check',
      departmentId: createdDepartments.find(d => d.name === 'Security')!.id,
      propertyId: createdProperties[0].id,
      input: 'Alarm status report',
      process: 'Test alarm daily/weekly',
      output: 'Working/Not Working',
      thresholdLimits: 'Should not be inactive for 1 hour',
      responsibleAgent: 'Security Vendor',
    },
    {
      name: 'Security',
      subFunction: 'Door Lock Check',
      departmentId: createdDepartments.find(d => d.name === 'Security')!.id,
      propertyId: createdProperties[0].id,
      input: 'Manual or digital lock status',
      process: 'Daily inspection',
      output: 'Locked/Unlocked',
      thresholdLimits: 'Must be locked at night',
      responsibleAgent: 'Security Guard',
    },
    {
      name: 'Water',
      subFunction: 'Motor Operation',
      departmentId: createdDepartments.find(d => d.name === 'Water')!.id,
      propertyId: createdProperties[0].id,
      input: 'Water level + Timer settings',
      process: 'Auto/manual motor control',
      output: 'Motor status: ON/OFF',
      thresholdLimits: 'Max runtime: 30 min',
      responsibleAgent: 'Electrician/Plumber',
    },
    {
      name: 'Water',
      subFunction: 'Overhead Tank Cleanliness',
      departmentId: createdDepartments.find(d => d.name === 'Water')!.id,
      propertyId: createdProperties[0].id,
      input: 'Inspection date, photos',
      process: 'Monthly inspection check',
      output: 'Clean/Unclean status',
      thresholdLimits: 'Max interval: 30 days',
      responsibleAgent: 'Housekeeper',
    },
    {
      name: 'Water',
      subFunction: 'Tank Level Monitoring',
      departmentId: createdDepartments.find(d => d.name === 'Water')!.id,
      propertyId: createdProperties[0].id,
      input: 'Sensor reading (%, time)',
      process: 'Fetch from sensor/API',
      output: 'Current water level (%)',
      thresholdLimits: 'Min: 20%, Max: 80%',
      responsibleAgent: 'Facility Supervisor',
    },
  ];

  for (const functionData of maintenanceFunctions) {
    const maintenanceFunction = await prisma.maintenanceFunction.create({
      data: functionData,
    });
    console.log('✅ Created maintenance function:', maintenanceFunction.name, '-', maintenanceFunction.subFunction);
  }

  // Create sample activities
  await prisma.activity.create({
    data: {
      userId: admin.id,
      propertyId: createdProperties[0].id,
      action: 'SYSTEM_INITIALIZATION',
      description: 'System initialized with sample data',
      metadata: {
        seedVersion: '1.0.0',
        timestamp: new Date().toISOString(),
      },
    },
  });

  // Create sample water systems
  const waterSystems = [
    {
      propertyId: createdProperties[0].id,
      tankName: 'Main Overhead Tank',
      capacity: 5000.0,
      currentLevel: 3500.0,
      levelPercentage: 70.0,
      pumpStatus: 'ON',
      flowRate: 25.0,
      pressure: 45.0,
      quality: 'GOOD',
      lastMaintenance: new Date('2024-12-01'),
      nextMaintenance: new Date('2025-06-01'),
    },
    {
      propertyId: createdProperties[0].id,
      tankName: 'Underground Tank',
      capacity: 10000.0,
      currentLevel: 8500.0,
      levelPercentage: 85.0,
      pumpStatus: 'OFF',
      flowRate: 0.0,
      pressure: 0.0,
      quality: 'GOOD',
      lastMaintenance: new Date('2024-11-15'),
      nextMaintenance: new Date('2025-05-15'),
    },
    {
      propertyId: createdProperties[1].id,
      tankName: 'Office Building Tank',
      capacity: 3000.0,
      currentLevel: 1200.0,
      levelPercentage: 40.0,
      pumpStatus: 'ON',
      flowRate: 20.0,
      pressure: 35.0,
      quality: 'FAIR',
      lastMaintenance: new Date('2024-10-01'),
      nextMaintenance: new Date('2025-04-01'),
    },
  ];

  for (const waterSystemData of waterSystems) {
    const waterSystem = await prisma.waterSystem.create({
      data: waterSystemData,
    });
    console.log('✅ Created water system:', waterSystem.tankName);
  }

  // Create sample electricity systems
  const electricitySystems = [
    {
      propertyId: createdProperties[0].id,
      systemName: 'Main Generator',
      generatorStatus: 'STANDBY',
      fuelLevel: 85.0,
      powerConsumption: 25.0,
      voltage: 230.0,
      frequency: 50.0,
      loadPercentage: 50.0,
      mainsPowerStatus: 'AVAILABLE',
      batteryBackup: 90.0,
      lastMaintenance: new Date('2024-11-01'),
      nextMaintenance: new Date('2025-05-01'),
    },
    {
      propertyId: createdProperties[0].id,
      systemName: 'Main Electrical Panel',
      generatorStatus: 'OFF',
      fuelLevel: 0.0,
      powerConsumption: 65.0,
      voltage: 240.0,
      frequency: 50.0,
      loadPercentage: 65.0,
      mainsPowerStatus: 'AVAILABLE',
      batteryBackup: 0.0,
      lastMaintenance: new Date('2024-12-15'),
      nextMaintenance: new Date('2025-06-15'),
    },
    {
      propertyId: createdProperties[1].id,
      systemName: 'Office Generator',
      generatorStatus: 'ON',
      fuelLevel: 60.0,
      powerConsumption: 15.0,
      voltage: 230.0,
      frequency: 50.0,
      loadPercentage: 50.0,
      mainsPowerStatus: 'UNAVAILABLE',
      batteryBackup: 75.0,
      lastMaintenance: new Date('2024-10-15'),
      nextMaintenance: new Date('2025-04-15'),
    },
  ];

  for (const electricitySystemData of electricitySystems) {
    const electricitySystem = await prisma.electricitySystem.create({
      data: electricitySystemData,
    });
    console.log('✅ Created electricity system:', electricitySystem.systemName);
  }

  // Create sample security systems
  const securitySystems = [
    {
      propertyId: createdProperties[0].id,
      systemName: 'Main Security System',
      cameraCount: 8,
      activeCameras: 7,
      accessPoints: 4,
      activeAccess: 4,
      alarmStatus: 'ARMED',
      motionDetected: false,
      lastMaintenance: new Date('2024-11-01'),
      nextMaintenance: new Date('2025-05-01'),
    },
    {
      propertyId: createdProperties[1].id,
      systemName: 'Office Security System',
      cameraCount: 12,
      activeCameras: 11,
      accessPoints: 6,
      activeAccess: 5,
      alarmStatus: 'DISARMED',
      motionDetected: false,
      lastMaintenance: new Date('2024-12-01'),
      nextMaintenance: new Date('2025-06-01'),
    },
    {
      propertyId: createdProperties[2].id,
      systemName: 'Construction Site Security',
      cameraCount: 4,
      activeCameras: 4,
      accessPoints: 2,
      activeAccess: 2,
      alarmStatus: 'ARMED',
      motionDetected: true,
      lastIncident: new Date('2024-12-20'),
      lastMaintenance: new Date('2024-10-15'),
      nextMaintenance: new Date('2025-04-15'),
    },
  ];

  for (const securitySystemData of securitySystems) {
    const securitySystem = await prisma.securitySystem.create({
      data: securitySystemData,
    });
    console.log('✅ Created security system:', securitySystem.systemName);
  }

  // Create sample attendance records for the last 7 days
  const attendanceRecords = [];
  const today = new Date();

  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    // Skip weekends for office employees
    if (date.getDay() === 0 || date.getDay() === 6) continue;

    for (const employeeData of employees.slice(0, 8)) { // First 8 employees
      const employee = await prisma.employee.findFirst({
        where: { employeeId: employeeData.employeeId }
      });

      if (!employee) continue;

      // Random attendance pattern (90% attendance rate)
      const isPresent = Math.random() > 0.1;

      if (isPresent) {
        const checkInTime = new Date(date);
        checkInTime.setHours(8 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60)); // 8-10 AM

        const checkOutTime = new Date(date);
        checkOutTime.setHours(17 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 60)); // 5-7 PM

        attendanceRecords.push({
          employeeId: employee.id,
          date: date,
          checkInTime: checkInTime,
          checkOutTime: checkOutTime,
          status: 'PRESENT' as const,
          hoursWorked: (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60),
        });
      } else {
        attendanceRecords.push({
          employeeId: employee.id,
          date: date,
          status: 'ABSENT' as const,
          hoursWorked: 0,
        });
      }
    }
  }

  for (const attendanceData of attendanceRecords) {
    await prisma.attendanceRecord.create({
      data: {
        ...attendanceData,
        officeId: createdOffices[0].id, // Default to first office
      },
    });
  }

  console.log(`✅ Created ${attendanceRecords.length} attendance records`);

  // Create some system status records
  const systemStatuses = [
    {
      propertyId: createdProperties[0].id,
      systemType: 'WATER' as const,
      status: 'OPERATIONAL' as const,
      lastChecked: new Date(),
      metadata: {
        tankLevel: 75,
        motorStatus: 'OFF',
        pressure: 'Normal',
      },
    },
    {
      propertyId: createdProperties[0].id,
      systemType: 'ELECTRICITY' as const,
      status: 'OPERATIONAL' as const,
      lastChecked: new Date(),
      metadata: {
        powerConsumption: 850,
        voltage: 230,
        frequency: 50,
      },
    },
    {
      propertyId: createdProperties[0].id,
      systemType: 'SECURITY' as const,
      status: 'WARNING' as const,
      lastChecked: new Date(),
      metadata: {
        camerasOnline: 11,
        camerasTotal: 12,
        alarmStatus: 'ARMED',
      },
    },
    {
      propertyId: createdProperties[0].id,
      systemType: 'INTERNET' as const,
      status: 'OPERATIONAL' as const,
      lastChecked: new Date(),
      metadata: {
        downloadSpeed: 95,
        uploadSpeed: 85,
        latency: 12,
      },
    },
    {
      propertyId: createdProperties[1].id,
      systemType: 'WATER' as const,
      status: 'WARNING' as const,
      lastChecked: new Date(),
      metadata: {
        tankLevel: 45,
        motorStatus: 'ON',
        pressure: 'Low',
      },
    },
  ];

  for (const statusData of systemStatuses) {
    await prisma.systemStatus.upsert({
      where: {
        propertyId_systemType: {
          propertyId: statusData.propertyId,
          systemType: statusData.systemType,
        },
      },
      update: statusData,
      create: statusData,
    });
  }

  console.log(`✅ Created ${systemStatuses.length} system status records`);

  // Seed comprehensive system content for all properties
  console.log('🔧 Seeding comprehensive system content...');
  await seedSystemContent(createdProperties);

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Summary:');
  console.log(`- Admin user: ${adminEmail} (password: ${adminPassword})`);
  console.log(`- Properties: ${createdProperties.length}`);
  console.log(`- Users: ${users.length + 1}`);
  console.log(`- Offices: ${createdOffices.length}`);
  console.log(`- Employees: ${employees.length}`);
  console.log(`- Alerts: ${alerts.length}`);
  console.log(`- Attendance Records: ${attendanceRecords.length}`);
  console.log(`- System Status Records: ${systemStatuses.length}`);
  console.log(`- Water Systems: ${waterSystems.length}`);
  console.log(`- Electricity Systems: ${electricitySystems.length}`);
  console.log(`- Security Systems: ${securitySystems.length}`);
}

// Comprehensive system content seeding function
async function seedSystemContent(properties: any[]) {
  const systemContentTemplates = [
    // WATER SYSTEM CONTENT
    {
      systemType: 'WATER' as const,
      contentType: 'contact',
      title: 'Municipal Water Board',
      content: {
        organization: 'City Water Authority',
        phone: '1916',
        email: '<EMAIL>',
        address: 'Water Board Office, City Center',
        emergencyContact: true,
        workingHours: '24/7',
        services: ['Water supply complaints', 'Quality issues', 'Billing queries']
      },
      displayOrder: 1,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'contact',
      title: 'Emergency Plumber',
      content: {
        organization: '24/7 Plumbing Services',
        phone: '+91 98765 12345',
        email: '<EMAIL>',
        address: 'Available citywide',
        emergencyContact: true,
        workingHours: '24/7',
        services: ['Emergency repairs', 'Pipe bursts', 'Tank cleaning', 'Motor repairs']
      },
      displayOrder: 2,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'maintenance_task',
      title: 'Monthly Tank Cleaning',
      content: {
        description: 'Clean and disinfect overhead and underground water tanks',
        frequency: 'Monthly',
        duration: '2-3 hours',
        requirements: ['Cleaning supplies', 'Safety equipment', 'Disinfectants'],
        steps: [
          'Drain the tank completely',
          'Scrub walls and floor with cleaning solution',
          'Rinse thoroughly with clean water',
          'Apply disinfectant and let it sit for 30 minutes',
          'Final rinse and refill'
        ],
        safetyNotes: ['Ensure proper ventilation', 'Use safety harness', 'Test water quality after cleaning']
      },
      displayOrder: 3,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'quality_parameter',
      title: 'pH Level Monitoring',
      content: {
        parameter: 'pH Level',
        normalRange: '6.5 - 8.5',
        testFrequency: 'Weekly',
        alertThreshold: 'Outside normal range',
        testMethod: 'Digital pH meter or test strips',
        correctionMethods: {
          'Low pH (< 6.5)': 'Add sodium bicarbonate',
          'High pH (> 8.5)': 'Add citric acid or vinegar'
        }
      },
      displayOrder: 4,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'quality_parameter',
      title: 'TDS (Total Dissolved Solids)',
      content: {
        parameter: 'TDS',
        normalRange: '150 - 300 ppm',
        testFrequency: 'Weekly',
        alertThreshold: '> 500 ppm',
        testMethod: 'TDS meter',
        healthImplications: 'High TDS may indicate contamination'
      },
      displayOrder: 5,
    },

    // ELECTRICITY SYSTEM CONTENT
    {
      systemType: 'ELECTRICITY' as const,
      contentType: 'contact',
      title: 'Electricity Board',
      content: {
        organization: 'State Electricity Board',
        phone: '1912',
        email: '<EMAIL>',
        address: 'Electricity Board Office, Main Road',
        emergencyContact: true,
        workingHours: '24/7',
        services: ['Power outage complaints', 'Billing issues', 'New connections']
      },
      displayOrder: 1,
    },
    {
      systemType: 'ELECTRICITY' as const,
      contentType: 'contact',
      title: 'Emergency Electrician',
      content: {
        organization: 'Quick Fix Electrical Services',
        phone: '+91 98765 54321',
        email: '<EMAIL>',
        address: 'Available 24/7',
        emergencyContact: true,
        workingHours: '24/7',
        services: ['Emergency repairs', 'Generator maintenance', 'Wiring issues', 'Panel repairs']
      },
      displayOrder: 2,
    },
    {
      systemType: 'ELECTRICITY' as const,
      contentType: 'maintenance_task',
      title: 'Monthly Generator Maintenance',
      content: {
        description: 'Regular maintenance of backup generator system',
        frequency: 'Monthly',
        duration: '1-2 hours',
        requirements: ['Engine oil', 'Air filter', 'Fuel filter', 'Coolant'],
        steps: [
          'Check engine oil level and quality',
          'Inspect air filter and clean if necessary',
          'Check fuel level and quality',
          'Test battery voltage and connections',
          'Run generator for 30 minutes under load',
          'Check all electrical connections'
        ],
        safetyNotes: ['Ensure proper ventilation', 'Never refuel while running', 'Check for gas leaks']
      },
      displayOrder: 3,
    },
    {
      systemType: 'ELECTRICITY' as const,
      contentType: 'monitoring_parameter',
      title: 'Power Consumption Tracking',
      content: {
        parameter: 'Daily Power Consumption',
        normalRange: '50 - 150 kWh/day',
        monitoringFrequency: 'Daily',
        alertThreshold: '> 200 kWh/day',
        costImplications: 'High consumption increases electricity bills',
        optimizationTips: ['Use LED lights', 'Optimize AC usage', 'Regular maintenance of appliances']
      },
      displayOrder: 4,
    },

    // SECURITY SYSTEM CONTENT
    {
      systemType: 'SECURITY' as const,
      contentType: 'contact',
      title: 'Local Police Station',
      content: {
        organization: 'City Police Station',
        phone: '100',
        email: '<EMAIL>',
        address: 'Police Station, Main Road',
        emergencyContact: true,
        workingHours: '24/7',
        services: ['Emergency response', 'Crime reporting', 'Security advice']
      },
      displayOrder: 1,
    },
    {
      systemType: 'SECURITY' as const,
      contentType: 'contact',
      title: 'Security System Technician',
      content: {
        organization: 'SecureTech Solutions',
        phone: '+91 98765 11111',
        email: '<EMAIL>',
        address: 'Tech Park, IT District',
        emergencyContact: false,
        workingHours: '9 AM - 6 PM',
        services: ['CCTV maintenance', 'Access control', 'Alarm systems', 'System upgrades']
      },
      displayOrder: 2,
    },
    {
      systemType: 'SECURITY' as const,
      contentType: 'maintenance_task',
      title: 'Monthly CCTV System Check',
      content: {
        description: 'Comprehensive check of all CCTV cameras and recording systems',
        frequency: 'Monthly',
        duration: '2-3 hours',
        requirements: ['Cleaning supplies', 'Backup storage devices', 'Testing equipment'],
        steps: [
          'Check all camera feeds for clarity',
          'Clean camera lenses and housings',
          'Verify recording functionality',
          'Test motion detection settings',
          'Check storage capacity and backup systems',
          'Update firmware if needed'
        ],
        safetyNotes: ['Use proper ladder safety', 'Check electrical connections', 'Test emergency recording']
      },
      displayOrder: 3,
    },
    {
      systemType: 'SECURITY' as const,
      contentType: 'monitoring_parameter',
      title: 'Camera Uptime Monitoring',
      content: {
        parameter: 'Camera Availability',
        normalRange: '95% - 100%',
        monitoringFrequency: 'Real-time',
        alertThreshold: '< 90%',
        impactAssessment: 'Reduced security coverage',
        troubleshootingSteps: ['Check power supply', 'Verify network connection', 'Restart camera system']
      },
      displayOrder: 4,
    },

    // INTERNET SYSTEM CONTENT
    {
      systemType: 'INTERNET' as const,
      contentType: 'contact',
      title: 'Internet Service Provider',
      content: {
        organization: 'Fiber Net Solutions',
        phone: '1800-123-4567',
        email: '<EMAIL>',
        address: 'ISP Office, Tech District',
        emergencyContact: false,
        workingHours: '24/7 Support',
        services: ['Connection issues', 'Speed problems', 'Billing support', 'Technical support']
      },
      displayOrder: 1,
    },
    {
      systemType: 'INTERNET' as const,
      contentType: 'maintenance_task',
      title: 'Weekly Network Performance Check',
      content: {
        description: 'Monitor and optimize network performance',
        frequency: 'Weekly',
        duration: '30 minutes',
        requirements: ['Speed testing tools', 'Network monitoring software'],
        steps: [
          'Run speed tests from multiple devices',
          'Check router and modem status',
          'Verify WiFi signal strength in all areas',
          'Test connectivity to critical services',
          'Check for firmware updates',
          'Monitor bandwidth usage'
        ],
        safetyNotes: ['Restart equipment during low usage hours', 'Keep backup connection ready']
      },
      displayOrder: 2,
    },
    {
      systemType: 'INTERNET' as const,
      contentType: 'monitoring_parameter',
      title: 'Internet Speed Monitoring',
      content: {
        parameter: 'Download/Upload Speed',
        normalRange: '80% of subscribed speed',
        monitoringFrequency: 'Daily',
        alertThreshold: '< 50% of subscribed speed',
        troubleshootingSteps: ['Restart router', 'Check cable connections', 'Contact ISP'],
        optimizationTips: ['Use wired connection for critical tasks', 'Optimize WiFi placement']
      },
      displayOrder: 3,
    },

    // MAINTENANCE SYSTEM CONTENT
    {
      systemType: 'MAINTENANCE' as const,
      contentType: 'contact',
      title: 'General Maintenance Contractor',
      content: {
        organization: 'All-Fix Maintenance Services',
        phone: '+91 98765 99999',
        email: '<EMAIL>',
        address: 'Service Center, Industrial Area',
        emergencyContact: true,
        workingHours: '8 AM - 8 PM',
        services: ['General repairs', 'Preventive maintenance', 'Emergency fixes', 'Equipment servicing']
      },
      displayOrder: 1,
    },
    {
      systemType: 'MAINTENANCE' as const,
      contentType: 'maintenance_task',
      title: 'Quarterly Building Inspection',
      content: {
        description: 'Comprehensive inspection of building systems and infrastructure',
        frequency: 'Quarterly',
        duration: '4-6 hours',
        requirements: ['Inspection checklist', 'Testing equipment', 'Safety gear'],
        steps: [
          'Inspect structural elements',
          'Check plumbing systems',
          'Test electrical systems',
          'Examine HVAC systems',
          'Review safety equipment',
          'Document findings and recommendations'
        ],
        safetyNotes: ['Use proper safety equipment', 'Follow lockout/tagout procedures', 'Document all issues']
      },
      displayOrder: 2,
    },

    // OTT SYSTEM CONTENT
    {
      systemType: 'OTT' as const,
      contentType: 'service_info',
      title: 'Netflix Subscription',
      content: {
        serviceName: 'Netflix',
        subscriptionType: 'Premium',
        monthlyFee: 'Rs. 649',
        features: ['4K streaming', 'Multiple screens', 'Download option'],
        renewalDate: '15th of every month',
        accountDetails: 'Managed by property admin',
        supportContact: 'Netflix Customer Support: 1800-123-4567'
      },
      displayOrder: 1,
    },
    {
      systemType: 'OTT' as const,
      contentType: 'service_info',
      title: 'Amazon Prime Video',
      content: {
        serviceName: 'Amazon Prime Video',
        subscriptionType: 'Annual',
        yearlyFee: 'Rs. 1499',
        features: ['HD streaming', 'Prime delivery benefits', 'Music included'],
        renewalDate: 'January 15th annually',
        accountDetails: 'Shared property account',
        supportContact: 'Amazon Support: 1800-419-7355'
      },
      displayOrder: 2,
    },
    {
      systemType: 'OTT' as const,
      contentType: 'service_info',
      title: 'Disney+ Hotstar',
      content: {
        serviceName: 'Disney+ Hotstar',
        subscriptionType: 'Super',
        yearlyFee: 'Rs. 899',
        features: ['Live sports', 'Disney content', 'Multiple languages'],
        renewalDate: 'March 10th annually',
        accountDetails: 'Property shared account',
        supportContact: 'Hotstar Support: <EMAIL>'
      },
      displayOrder: 3,
    }
  ];

  // Tab content templates for rich text editor
  const tabContentTemplates = [
    // WATER SYSTEM TABS
    {
      systemType: 'WATER' as const,
      contentType: 'tab_content',
      title: 'Overview',
      content: {},
      richContent: `# Water System Overview

Welcome to the water management system for this property.

## System Information
- **Total Capacity**: 5000 liters
- **Active Tanks**: 2 overhead tanks
- **Pump Systems**: 2 pumps (gate & compound)
- **Municipal Connections**: 4 connections

## Current Status
- All systems operational
- Regular maintenance schedule active
- Water quality monitoring in place

## Quick Actions
- View tank levels
- Check pump status
- Review maintenance schedule
- Contact emergency services`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'water_drop',
      displayOrder: 1,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'tab_content',
      title: 'Quality Monitoring',
      content: {},
      richContent: `# Water Quality Monitoring

## Current Parameters
- **pH Level**: 7.2 (Normal)
- **TDS**: 180 ppm (Good)
- **Chlorine**: 0.5 ppm (Safe)
- **Last Tested**: Today

## Testing Schedule
- **Daily**: Visual inspection
- **Weekly**: pH and TDS testing
- **Monthly**: Comprehensive analysis

## Quality Standards
- pH: 6.5 - 8.5
- TDS: 150 - 300 ppm
- Chlorine: 0.2 - 0.5 ppm

## Actions Required
- Weekly pH test due tomorrow
- Monthly comprehensive test due in 2 weeks`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'analytics',
      displayOrder: 2,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'tab_content',
      title: 'Maintenance',
      content: {},
      richContent: `# Water System Maintenance

## Upcoming Tasks
- **Tank Cleaning**: Due in 5 days
- **Pump Service**: Due in 2 weeks
- **Filter Replacement**: Due next month

## Maintenance History
- **Last Tank Cleaning**: 25 days ago (Completed)
- **Last Pump Service**: 45 days ago (Completed)
- **Last Filter Change**: 60 days ago (Completed)

## Emergency Procedures
1. **Water Shortage**: Switch to backup tank
2. **Pump Failure**: Contact emergency plumber
3. **Quality Issues**: Stop usage, test immediately

## Maintenance Contacts
- **Primary Plumber**: +91 98765 12345
- **Tank Cleaning**: +91 98765 54321`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'build',
      displayOrder: 3,
    },
    {
      systemType: 'WATER' as const,
      contentType: 'tab_content',
      title: 'Contacts',
      content: {},
      richContent: `# Emergency Contacts

## Municipal Services
- **Water Board**: 1916 (24/7)
- **Emergency Helpline**: 100

## Service Providers
- **Emergency Plumber**: +91 98765 12345
- **Tank Cleaning Service**: +91 98765 54321
- **Water Quality Testing**: +91 98765 67890

## Property Management
- **Property Manager**: +91 98765 11111
- **Maintenance Supervisor**: +91 98765 22222

## Important Notes
- Keep this list updated
- Test all numbers quarterly
- Emergency numbers are available 24/7`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'contact_phone',
      displayOrder: 4,
    },

    // ELECTRICITY SYSTEM TABS
    {
      systemType: 'ELECTRICITY' as const,
      contentType: 'tab_content',
      title: 'Overview',
      content: {},
      richContent: `# Electricity System Overview

## System Information
- **Main Connection**: 3-phase, 100 KVA
- **Backup Generator**: 75 KVA Diesel
- **Solar Panels**: 20 KW capacity
- **UPS Systems**: 5 units for critical loads

## Current Status
- Grid power: Available
- Generator: Standby mode
- Solar generation: 12 KW (active)
- Battery backup: 85% charged

## Monthly Consumption
- **This Month**: 2,450 kWh
- **Last Month**: 2,680 kWh
- **Average**: 2,500 kWh`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'electrical_services',
      displayOrder: 1,
    },
    {
      systemType: 'ELECTRICITY' as const,
      contentType: 'tab_content',
      title: 'Generator',
      content: {},
      richContent: `# Generator Management

## Generator Status
- **Model**: Cummins 75 KVA
- **Fuel Level**: 85%
- **Last Service**: 30 days ago
- **Next Service**: Due in 30 days
- **Running Hours**: 1,245 hours

## Maintenance Schedule
- **Weekly**: Visual inspection, fuel check
- **Monthly**: Oil change, filter check
- **Quarterly**: Comprehensive service

## Operating Procedures
1. **Auto Start**: Activates on power failure
2. **Manual Start**: Use control panel
3. **Emergency Stop**: Red button on panel

## Fuel Management
- **Capacity**: 500 liters
- **Current**: 425 liters
- **Consumption**: ~15 L/hour`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'power',
      displayOrder: 2,
    },

    // SECURITY SYSTEM TABS
    {
      systemType: 'SECURITY' as const,
      contentType: 'tab_content',
      title: 'Overview',
      content: {},
      richContent: `# Security System Overview

## System Components
- **CCTV Cameras**: 16 cameras (all operational)
- **Access Control**: 4 entry points
- **Alarm System**: Armed 24/7
- **Security Personnel**: 2 guards on duty

## Current Status
- All cameras online
- Recording: Active
- Motion detection: Enabled
- Last incident: None (30 days)

## Coverage Areas
- Main entrance
- Parking area
- Building perimeter
- Common areas`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'security',
      displayOrder: 1,
    },
    {
      systemType: 'SECURITY' as const,
      contentType: 'tab_content',
      title: 'CCTV',
      content: {},
      richContent: `# CCTV Monitoring

## Camera Status
- **Total Cameras**: 16
- **Online**: 16
- **Recording**: Active
- **Storage**: 85% available

## Camera Locations
1. **Main Gate**: 2 cameras (entry/exit)
2. **Parking Area**: 4 cameras (full coverage)
3. **Building Entrance**: 2 cameras
4. **Corridors**: 4 cameras
5. **Perimeter**: 4 cameras

## Recording Settings
- **Resolution**: 1080p
- **Frame Rate**: 30 fps
- **Retention**: 30 days
- **Motion Detection**: Enabled

## Maintenance Schedule
- **Weekly**: Camera cleaning
- **Monthly**: System check
- **Quarterly**: Storage optimization`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'videocam',
      displayOrder: 2,
    },

    // INTERNET SYSTEM TABS
    {
      systemType: 'INTERNET' as const,
      contentType: 'tab_content',
      title: 'Overview',
      content: {},
      richContent: `# Internet System Overview

## Connection Details
- **Primary ISP**: Fiber Net Solutions
- **Plan**: 1 Gbps Fiber
- **Backup ISP**: Mobile Hotspot
- **WiFi Networks**: 3 networks

## Current Status
- **Connection**: Active
- **Speed**: 950 Mbps down / 850 Mbps up
- **Latency**: 12ms
- **Uptime**: 99.8%

## Network Equipment
- **Main Router**: Cisco Enterprise
- **WiFi Access Points**: 5 units
- **Network Switch**: 24-port managed
- **Firewall**: Active protection

## Usage Statistics
- **Monthly Data**: 2.5 TB
- **Peak Usage**: 8 PM - 11 PM
- **Connected Devices**: 45`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'wifi',
      displayOrder: 1,
    },
    {
      systemType: 'INTERNET' as const,
      contentType: 'tab_content',
      title: 'Network Status',
      content: {},
      richContent: `# Network Status

## Speed Test Results
- **Download**: 950 Mbps
- **Upload**: 850 Mbps
- **Ping**: 12ms
- **Last Tested**: 2 hours ago

## WiFi Networks
1. **PropertyMain**: 5GHz, WPA3
2. **PropertyGuest**: 2.4GHz, WPA2
3. **PropertyIoT**: 2.4GHz, Isolated

## Connected Devices
- **Computers**: 12
- **Mobile Devices**: 18
- **Smart Home**: 15
- **Total**: 45 devices

## Troubleshooting
- Restart router if speed < 500 Mbps
- Check cables for physical damage
- Contact ISP if issues persist`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'network_check',
      displayOrder: 2,
    },

    // MAINTENANCE SYSTEM TABS
    {
      systemType: 'MAINTENANCE' as const,
      contentType: 'tab_content',
      title: 'Overview',
      content: {},
      richContent: `# Maintenance Overview

## Current Tasks
- **Open Issues**: 3
- **In Progress**: 2
- **Completed This Month**: 15
- **Scheduled**: 8

## Priority Breakdown
- **Critical**: 0
- **High**: 1
- **Medium**: 2
- **Low**: 2

## System Health
- **Water Systems**: Good
- **Electrical**: Excellent
- **Security**: Good
- **Internet**: Excellent

## Upcoming Maintenance
- Generator service (5 days)
- CCTV cleaning (1 week)
- Tank cleaning (2 weeks)`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'build',
      displayOrder: 1,
    },
    {
      systemType: 'MAINTENANCE' as const,
      contentType: 'tab_content',
      title: 'Schedule',
      content: {},
      richContent: `# Maintenance Schedule

## This Week
- **Monday**: Routine inspection
- **Wednesday**: Generator test run
- **Friday**: CCTV system check

## This Month
- **Week 2**: Tank cleaning
- **Week 3**: Electrical panel inspection
- **Week 4**: Security system maintenance

## Quarterly Tasks
- **Q1**: Comprehensive building inspection
- **Q2**: HVAC system service
- **Q3**: Fire safety equipment check
- **Q4**: Annual compliance review

## Emergency Contacts
- **General Maintenance**: +91 98765 99999
- **Electrical**: +91 98765 54321
- **Plumbing**: +91 98765 12345`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'schedule',
      displayOrder: 2,
    },

    // OTT SYSTEM TABS
    {
      systemType: 'OTT' as const,
      contentType: 'tab_content',
      title: 'Overview',
      content: {},
      richContent: `# OTT Services Overview

## Active Subscriptions
- **Netflix**: Premium (4K)
- **Amazon Prime**: Annual
- **Disney+ Hotstar**: Super
- **YouTube Premium**: Family

## Monthly Cost
- **Total**: Rs. 1,200
- **Per Unit**: Rs. 300
- **Renewal**: Auto-pay enabled

## Account Management
- **Primary Account**: Property Admin
- **User Profiles**: 12 active
- **Device Limit**: 4 concurrent streams

## Usage Statistics
- **Most Watched**: Netflix (65%)
- **Peak Hours**: 7 PM - 11 PM
- **Monthly Hours**: 450 hours`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'tv',
      displayOrder: 1,
    },
    {
      systemType: 'OTT' as const,
      contentType: 'tab_content',
      title: 'Subscriptions',
      content: {},
      richContent: `# Subscription Details

## Netflix Premium
- **Plan**: 4K Ultra HD
- **Cost**: Rs. 649/month
- **Renewal**: 15th every month
- **Screens**: 4 simultaneous
- **Features**: 4K, HDR, Downloads

## Amazon Prime Video
- **Plan**: Annual membership
- **Cost**: Rs. 1,499/year
- **Renewal**: January 15th
- **Benefits**: Prime delivery included
- **Features**: HD streaming, Music

## Disney+ Hotstar Super
- **Plan**: Annual
- **Cost**: Rs. 899/year
- **Renewal**: March 10th
- **Features**: Live sports, Disney content
- **Languages**: Multiple regional

## YouTube Premium Family
- **Plan**: Family (6 members)
- **Cost**: Rs. 189/month
- **Features**: Ad-free, Background play
- **Music**: YouTube Music included`,
      contentFormat: 'markdown',
      isTab: true,
      tabIcon: 'subscriptions',
      displayOrder: 2,
    },
  ];

  // Create system configurations for all properties
  for (const property of properties) {
    console.log(`  Creating system configurations for ${property.name}...`);

    // Create default system configurations
    const systemTypes = ['WATER', 'ELECTRICITY', 'SECURITY', 'INTERNET', 'MAINTENANCE', 'OTT'];

    for (const systemType of systemTypes) {
      const existingConfig = await prisma.propertySystemConfig.findFirst({
        where: {
          propertyId: property.id,
          systemType: systemType as any,
        },
      });

      if (!existingConfig) {
        await prisma.propertySystemConfig.create({
          data: {
            propertyId: property.id,
            systemType: systemType as any,
            displayName: `${systemType.charAt(0) + systemType.slice(1).toLowerCase()} Management`,
            displayOrder: systemTypes.indexOf(systemType) + 1,
            isEnabled: true,
            configuration: {},
          },
        });
        console.log(`    ✅ Created ${systemType} configuration`);
      }
    }

    // Create system content for each property
    for (const template of systemContentTemplates) {
      const existingContent = await prisma.systemContent.findFirst({
        where: {
          propertyId: property.id,
          systemType: template.systemType,
          contentType: template.contentType,
          title: template.title,
        },
      });

      if (!existingContent) {
        await prisma.systemContent.create({
          data: {
            propertyId: property.id,
            systemType: template.systemType,
            contentType: template.contentType,
            title: template.title,
            content: template.content,
            displayOrder: template.displayOrder,
            isActive: true,
          },
        });
        console.log(`    ✅ Created ${template.systemType} content: ${template.title}`);
      }
    }

    // Create tab content for each property
    for (const tabTemplate of tabContentTemplates) {
      const existingTab = await prisma.systemContent.findFirst({
        where: {
          propertyId: property.id,
          systemType: tabTemplate.systemType,
          contentType: tabTemplate.contentType,
          title: tabTemplate.title,
          isTab: true,
        },
      });

      if (!existingTab) {
        await prisma.systemContent.create({
          data: {
            propertyId: property.id,
            systemType: tabTemplate.systemType,
            contentType: tabTemplate.contentType,
            title: tabTemplate.title,
            content: tabTemplate.content,
            richContent: tabTemplate.richContent,
            contentFormat: tabTemplate.contentFormat,
            isTab: tabTemplate.isTab,
            tabIcon: tabTemplate.tabIcon,
            displayOrder: tabTemplate.displayOrder,
            isActive: true,
          },
        });
        console.log(`    ✅ Created ${tabTemplate.systemType} tab: ${tabTemplate.title}`);
      }
    }
  }

  console.log('✅ System content seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

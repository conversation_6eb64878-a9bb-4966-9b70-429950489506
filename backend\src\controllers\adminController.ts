import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const createRoleSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
});

const updateRoleSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
});

const assignRoleSchema = z.object({
  userId: z.string(),
  roleId: z.string(),
});

// Get all roles
export const getRoles = async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;
    
    // Only SUPER_ADMIN can view all roles
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    const roles = await prisma.role.findMany({
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        },
        _count: {
          select: {
            users: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    const formattedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        description: rp.permission.description,
        category: rp.permission.category
      })),
      userCount: role._count.users,
      users: role.users.map(ur => ur.user),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }));

    res.json({
      success: true,
      data: formattedRoles
    });
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles'
    });
  }
};

// Get role by ID
export const getRoleById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        permissions: {
          include: {
            permission: true
          }
        },
        users: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    const formattedRole = {
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: role.permissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        description: rp.permission.description,
        category: rp.permission.category
      })),
      users: role.users.map(ur => ur.user),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    };

    res.json({
      success: true,
      data: formattedRole
    });
  } catch (error) {
    console.error('Error fetching role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch role'
    });
  }
};

// Create new role
export const createRole = async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    const validatedData = createRoleSchema.parse(req.body);

    // Check if role name already exists
    const existingRole = await prisma.role.findUnique({
      where: { name: validatedData.name }
    });

    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: 'Role with this name already exists'
      });
    }

    // Create role
    const role = await prisma.role.create({
      data: {
        name: validatedData.name,
        description: validatedData.description
      }
    });

    // Assign permissions if provided
    if (validatedData.permissions && validatedData.permissions.length > 0) {
      const permissionAssignments = validatedData.permissions.map(permissionId => ({
        roleId: role.id,
        permissionId
      }));

      await prisma.rolePermission.createMany({
        data: permissionAssignments
      });
    }

    // Fetch the created role with permissions
    const createdRole = await prisma.role.findUnique({
      where: { id: role.id },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: createdRole,
      message: 'Role created successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error creating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create role'
    });
  }
};

// Update role
export const updateRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    const validatedData = updateRoleSchema.parse(req.body);

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id }
    });

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if new name conflicts with existing role
    if (validatedData.name && validatedData.name !== existingRole.name) {
      const nameConflict = await prisma.role.findUnique({
        where: { name: validatedData.name }
      });

      if (nameConflict) {
        return res.status(400).json({
          success: false,
          message: 'Role with this name already exists'
        });
      }
    }

    // Update role
    const updatedRole = await prisma.role.update({
      where: { id },
      data: {
        name: validatedData.name,
        description: validatedData.description
      }
    });

    // Update permissions if provided
    if (validatedData.permissions) {
      // Remove existing permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: id }
      });

      // Add new permissions
      if (validatedData.permissions.length > 0) {
        const permissionAssignments = validatedData.permissions.map(permissionId => ({
          roleId: id,
          permissionId
        }));

        await prisma.rolePermission.createMany({
          data: permissionAssignments
        });
      }
    }

    // Fetch updated role with permissions
    const roleWithPermissions = await prisma.role.findUnique({
      where: { id },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: roleWithPermissions,
      message: 'Role updated successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error updating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role'
    });
  }
};

// Delete role
export const deleteRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        users: true
      }
    });

    if (!existingRole) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if role is assigned to any users
    if (existingRole.users.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete role that is assigned to users'
      });
    }

    // Delete role permissions first
    await prisma.rolePermission.deleteMany({
      where: { roleId: id }
    });

    // Delete role
    await prisma.role.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete role'
    });
  }
};

// Get all permissions
export const getPermissions = async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;

    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    const permissions = await prisma.permission.findMany({
      include: {
        roles: {
          include: {
            role: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        _count: {
          select: {
            roles: true
          }
        }
      },
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    });

    const formattedPermissions = permissions.map(permission => ({
      id: permission.id,
      name: permission.name,
      description: permission.description,
      category: permission.category,
      roles: permission.roles.map(rp => rp.role),
      roleCount: permission._count.roles,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt
    }));

    res.json({
      success: true,
      data: formattedPermissions
    });
  } catch (error) {
    console.error('Error fetching permissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions'
    });
  }
};

// Assign role to user
export const assignUserRole = async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;

    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    const validatedData = assignRoleSchema.parse(req.body);

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: validatedData.userId }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: validatedData.roleId }
    });

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if assignment already exists
    const existingAssignment = await prisma.userRoleAssignment.findUnique({
      where: {
        userId_roleId: {
          userId: validatedData.userId,
          roleId: validatedData.roleId
        }
      }
    });

    if (existingAssignment) {
      return res.status(400).json({
        success: false,
        message: 'User already has this role'
      });
    }

    // Create assignment
    const assignment = await prisma.userRoleAssignment.create({
      data: {
        userId: validatedData.userId,
        roleId: validatedData.roleId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        role: {
          select: {
            id: true,
            name: true,
            description: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: assignment,
      message: 'Role assigned to user successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error assigning role to user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign role to user'
    });
  }
};

// Remove role from user
export const removeUserRole = async (req: Request, res: Response) => {
  try {
    const { userId, roleId } = req.params;
    const userRole = req.user?.role;

    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Super admin privileges required.'
      });
    }

    // Check if assignment exists
    const existingAssignment = await prisma.userRoleAssignment.findUnique({
      where: {
        userId_roleId: {
          userId,
          roleId
        }
      }
    });

    if (!existingAssignment) {
      return res.status(404).json({
        success: false,
        message: 'Role assignment not found'
      });
    }

    // Remove assignment
    await prisma.userRoleAssignment.delete({
      where: {
        userId_roleId: {
          userId,
          roleId
        }
      }
    });

    res.json({
      success: true,
      message: 'Role removed from user successfully'
    });
  } catch (error) {
    console.error('Error removing role from user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove role from user'
    });
  }
};

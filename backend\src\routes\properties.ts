import { Router } from 'express';
import { UserRole, SystemType } from '@prisma/client';
import { prisma } from '@/lib/prisma';
import { 
  createPropertySchema, 
  updatePropertySchema, 
  updateSystemSchema,
  propertyQuerySchema,
  paginationSchema 
} from '@/lib/validation';
import { validateRequest, asyncHandler, AppError } from '@/middleware/errorHandler';
import { authenticate, authorize, requirePropertyAccess, requireAction } from '@/middleware/auth';
import { conditionalRateLimit } from '@/middleware/rateLimiter';
import { 
  PropertyDetail, 
  CreatePropertyRequest, 
  UpdateSystemRequest,
  PaginatedResponse,
  PropertySummary,
  SystemStatusSummary 
} from '@/types';

const router = Router();

// Apply authentication and rate limiting to all routes
router.use(authenticate);
router.use(conditionalRateLimit);

/**
 * @swagger
 * /properties:
 *   get:
 *     tags: [Properties]
 *     summary: List properties
 *     description: Get paginated list of properties with filtering and RBAC
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [RESIDENTIAL, OFFICE, CONSTRUCTION]
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Properties retrieved successfully
 */
router.get('/', 
  authorize([], 'canViewDashboard'),
  validateRequest(propertyQuerySchema, 'query'),
  asyncHandler(async (req, res) => {
    const { page, limit, type, search } = req.query as any;
    const user = req.user!;

    // Build where clause based on RBAC
    let whereClause: any = {};

    // Super admins can see all properties
    if (user.role !== UserRole.SUPER_ADMIN) {
      whereClause.id = {
        in: user.assignedProperties,
      };
    }

    // Apply filters
    if (type) {
      whereClause.type = type;
    }

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await prisma.property.count({ where: whereClause });

    // Get properties with pagination
    const properties = await prisma.property.findMany({
      where: whereClause,
      include: {
        systemStatuses: {
          select: {
            systemType: true,
            status: true,
            healthScore: true,
            lastChecked: true,
          },
        },
        _count: {
          select: {
            alerts: {
              where: {
                status: 'OPEN',
              },
            },
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Transform to PropertySummary format
    const propertySummaries: PropertySummary[] = properties.map(property => {
      const avgHealthScore = property.systemStatuses.length > 0
        ? property.systemStatuses.reduce((sum, status) => sum + (status.healthScore || 0), 0) / property.systemStatuses.length
        : 100;

      const criticalSystems = property.systemStatuses.filter(s => s.status === 'CRITICAL').length;
      const warningSystems = property.systemStatuses.filter(s => s.status === 'WARNING').length;
      
      let overallStatus = 'OPERATIONAL';
      if (criticalSystems > 0) overallStatus = 'CRITICAL';
      else if (warningSystems > 0) overallStatus = 'WARNING';

      return {
        id: property.id,
        name: property.name,
        type: property.type,
        status: overallStatus,
        healthScore: Math.round(avgHealthScore),
        alertCount: property._count.alerts,
        lastUpdate: property.updatedAt.toISOString(),
      };
    });

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<PropertySummary> = {
      data: propertySummaries,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /properties:
 *   post:
 *     tags: [Properties]
 *     summary: Create new property
 *     description: Create a new property (Admin/Property Manager only)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePropertyRequest'
 *     responses:
 *       201:
 *         description: Property created successfully
 *       403:
 *         description: Insufficient permissions
 */
router.post('/',
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER], 'canManageProperties'),
  requireAction('create'),
  validateRequest(createPropertySchema),
  asyncHandler(async (req, res) => {
    const propertyData: CreatePropertyRequest = req.body;
    const user = req.user!;

    // Create property
    const property = await prisma.property.create({
      data: {
        name: propertyData.name,
        type: propertyData.type,
        address: propertyData.address,
        description: propertyData.description,
        latitude: propertyData.latitude,
        longitude: propertyData.longitude,
        images: propertyData.images || [],
      },
    });

    // Create default system statuses
    const systemTypes: SystemType[] = ['WATER', 'ELECTRICITY', 'SECURITY', 'INTERNET', 'OTT', 'MAINTENANCE'];

    await prisma.systemStatus.createMany({
      data: systemTypes.map(systemType => ({
        propertyId: property.id,
        systemType,
        status: 'OPERATIONAL',
        description: `${systemType.toLowerCase()} system initialized`,
        healthScore: 100,
      })),
    });

    // Create default property system configurations
    const defaultSystemConfigs = [
      { systemType: SystemType.WATER, displayName: 'Water Management', displayOrder: 1 },
      { systemType: SystemType.ELECTRICITY, displayName: 'Electricity Management', displayOrder: 2 },
      { systemType: SystemType.SECURITY, displayName: 'Security Management', displayOrder: 3 },
      { systemType: SystemType.INTERNET, displayName: 'Internet Management', displayOrder: 4 },
      { systemType: SystemType.MAINTENANCE, displayName: 'Maintenance Management', displayOrder: 5 },
    ];

    await prisma.propertySystemConfig.createMany({
      data: defaultSystemConfigs.map(config => ({
        propertyId: property.id,
        ...config,
        isEnabled: true,
        configuration: {},
      })),
    });

    // Assign property to creator if not super admin
    if (user.role !== UserRole.SUPER_ADMIN) {
      await prisma.userProperty.create({
        data: {
          userId: user.id,
          propertyId: property.id,
        },
      });
    }

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        propertyId: property.id,
        action: 'CREATE_PROPERTY',
        description: `Created property: ${property.name}`,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.status(201).json({
      success: true,
      data: property,
      message: 'Property created successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /properties/{propertyId}:
 *   get:
 *     tags: [Properties]
 *     summary: Get property details
 *     description: Retrieve detailed property information including system statuses
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Property retrieved successfully
 *       404:
 *         description: Property not found
 */
router.get('/:propertyId',
  requirePropertyAccess(),
  asyncHandler(async (req, res) => {
    const { propertyId } = req.params;

    const property = await prisma.property.findUnique({
      where: { id: propertyId },
      include: {
        systemStatuses: {
          orderBy: { systemType: 'asc' },
        },
        activities: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        alerts: {
          where: {
            status: 'OPEN',
          },
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
      },
    });

    if (!property) {
      throw new AppError('Property not found', 404, 'PROPERTY_NOT_FOUND');
    }

    // Calculate statistics
    const systemStatuses = property.systemStatuses;
    const totalSystems = systemStatuses.length;
    const operationalSystems = systemStatuses.filter(s => s.status === 'OPERATIONAL').length;
    const warningSystems = systemStatuses.filter(s => s.status === 'WARNING').length;
    const criticalSystems = systemStatuses.filter(s => s.status === 'CRITICAL').length;
    const offlineSystems = systemStatuses.filter(s => s.status === 'OFFLINE').length;
    
    const averageHealthScore = totalSystems > 0
      ? systemStatuses.reduce((sum, status) => sum + (status.healthScore || 0), 0) / totalSystems
      : 100;

    // Transform system statuses
    const systemStatusSummaries: SystemStatusSummary[] = systemStatuses.map(status => ({
      systemType: status.systemType,
      status: status.status,
      description: status.description || undefined,
      healthScore: status.healthScore || undefined,
      lastChecked: status.lastChecked.toISOString(),
    }));

    const propertyDetail: PropertyDetail = {
      ...property,
      systemStatuses: systemStatusSummaries,
      recentActivities: property.activities,
      statistics: {
        totalSystems,
        operationalSystems,
        warningsSystems: warningSystems,
        criticalSystems,
        offlineSystems,
        averageHealthScore: Math.round(averageHealthScore),
        uptime: Math.round((operationalSystems / totalSystems) * 100),
        lastIncident: property.alerts[0]?.createdAt.toISOString(),
      },
    };

    res.json({
      success: true,
      data: propertyDetail,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /properties/{propertyId}/systems:
 *   get:
 *     tags: [Properties]
 *     summary: Get property system statuses
 *     description: Retrieve all system statuses for a property
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: systemType
 *         schema:
 *           type: string
 *           enum: [WATER, ELECTRICITY, SECURITY, INTERNET, OTT, MAINTENANCE]
 *     responses:
 *       200:
 *         description: System statuses retrieved successfully
 */
router.get('/:propertyId/systems',
  requirePropertyAccess(),
  asyncHandler(async (req, res) => {
    const { propertyId } = req.params;
    const { systemType } = req.query;

    let whereClause: any = { propertyId };
    if (systemType) {
      whereClause.systemType = systemType;
    }

    const systemStatuses = await prisma.systemStatus.findMany({
      where: whereClause,
      orderBy: { systemType: 'asc' },
    });

    const systemStatusSummaries: SystemStatusSummary[] = systemStatuses.map(status => ({
      systemType: status.systemType,
      status: status.status,
      description: status.description || undefined,
      healthScore: status.healthScore || undefined,
      lastChecked: status.lastChecked.toISOString(),
    }));

    res.json({
      success: true,
      data: systemStatusSummaries,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /properties/{propertyId}/systems/{systemType}:
 *   get:
 *     tags: [Properties]
 *     summary: Get specific system details
 *     description: Retrieve detailed information for a specific system type
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: systemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [WATER, ELECTRICITY, SECURITY, INTERNET, OTT, MAINTENANCE]
 *     responses:
 *       200:
 *         description: System details retrieved successfully
 *       404:
 *         description: System not found
 */
router.get('/:propertyId/systems/:systemType',
  requirePropertyAccess(),
  asyncHandler(async (req, res) => {
    const { propertyId, systemType } = req.params;

    const systemStatus = await prisma.systemStatus.findUnique({
      where: {
        propertyId_systemType: {
          propertyId,
          systemType: systemType as SystemType,
        },
      },
    });

    if (!systemStatus) {
      throw new AppError('System status not found', 404, 'SYSTEM_NOT_FOUND');
    }

    res.json({
      success: true,
      data: systemStatus,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /properties/{propertyId}/systems/{systemType}:
 *   put:
 *     tags: [Properties]
 *     summary: Update system status
 *     description: Update system status and metadata
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: systemType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [WATER, ELECTRICITY, SECURITY, INTERNET, OTT, MAINTENANCE]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateSystemRequest'
 *     responses:
 *       200:
 *         description: System updated successfully
 */
router.put('/:propertyId/systems/:systemType',
  requirePropertyAccess(),
  authorize([], 'canManageProperties'),
  requireAction('update'),
  validateRequest(updateSystemSchema),
  asyncHandler(async (req, res) => {
    const { propertyId, systemType } = req.params;
    const updateData: UpdateSystemRequest = req.body;
    const user = req.user!;

    // Check if system exists
    const existingSystem = await prisma.systemStatus.findUnique({
      where: {
        propertyId_systemType: {
          propertyId,
          systemType: systemType as SystemType,
        },
      },
    });

    if (!existingSystem) {
      throw new AppError('System status not found', 404, 'SYSTEM_NOT_FOUND');
    }

    // Update system status
    const updatedSystem = await prisma.systemStatus.update({
      where: {
        propertyId_systemType: {
          propertyId,
          systemType: systemType as SystemType,
        },
      },
      data: {
        status: updateData.status,
        description: updateData.description,
        metadata: updateData.metadata,
        healthScore: updateData.healthScore,
        lastChecked: new Date(),
      },
    });

    // Create alert if status is critical
    if (updateData.status === 'CRITICAL') {
      await prisma.alert.create({
        data: {
          propertyId,
          title: `Critical ${systemType} System Alert`,
          message: updateData.description || `${systemType} system is in critical state`,
          severity: 'CRITICAL',
          category: systemType,
          metadata: {
            systemType,
            previousStatus: existingSystem.status,
            newStatus: updateData.status,
          },
        },
      });
    }

    // Log activity
    await prisma.activity.create({
      data: {
        userId: user.id,
        propertyId,
        action: 'UPDATE_SYSTEM',
        description: `Updated ${systemType} system status to ${updateData.status}`,
        metadata: {
          systemType,
          previousStatus: existingSystem.status,
          newStatus: updateData.status,
        },
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
      },
    });

    res.json({
      success: true,
      data: updatedSystem,
      message: 'System status updated successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

// Get property user assignments
router.get('/:propertyId/users',
  requirePropertyAccess(),
  asyncHandler(async (req, res) => {
    const { propertyId } = req.params;

    // Get all users assigned to this property via UserProperty relation
    const userProperties = await prisma.userProperty.findMany({
      where: {
        propertyId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            phone: true,
            isActive: true,
            createdAt: true,
          },
        },
      },
    });

    // Filter active users and transform data
    const assignedUsers = userProperties
      .filter(up => up.user.isActive)
      .map(up => ({
        id: up.user.id,
        name: up.user.name,
        email: up.user.email,
        role: up.user.role,
        phone: up.user.phone,
        assignedProperties: [propertyId], // Simplified for this context
        createdAt: up.user.createdAt,
      }));

    res.json({
      success: true,
      data: assignedUsers,
    });
  })
);

// Assign user to property
router.post('/:propertyId/users/:userId',
  requirePropertyAccess(),
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  asyncHandler(async (req, res) => {
    const { propertyId, userId } = req.params;

    // Check if user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        isActive: true,
      },
    });

    if (!targetUser) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND');
    }

    if (!targetUser.isActive) {
      throw new AppError('Cannot assign inactive user', 400, 'USER_INACTIVE');
    }

    // Check if assignment already exists
    const existingAssignment = await prisma.userProperty.findUnique({
      where: {
        userId_propertyId: {
          userId,
          propertyId,
        },
      },
    });

    if (existingAssignment) {
      return res.json({
        success: true,
        data: {
          ...targetUser,
          assignedProperties: [propertyId],
        },
        message: 'User already assigned to property',
      });
    }

    // Create the assignment
    await prisma.userProperty.create({
      data: {
        userId,
        propertyId,
      },
    });

    res.json({
      success: true,
      data: {
        ...targetUser,
        assignedProperties: [propertyId],
      },
      message: 'User assigned to property successfully',
    });
  })
);

// Remove user from property
router.delete('/:propertyId/users/:userId',
  requirePropertyAccess(),
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  asyncHandler(async (req, res) => {
    const { propertyId, userId } = req.params;

    // Check if user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
      },
    });

    if (!targetUser) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND');
    }

    // Check if assignment exists
    const existingAssignment = await prisma.userProperty.findUnique({
      where: {
        userId_propertyId: {
          userId,
          propertyId,
        },
      },
    });

    if (!existingAssignment) {
      return res.json({
        success: true,
        data: {
          ...targetUser,
          assignedProperties: [],
        },
        message: 'User was not assigned to this property',
      });
    }

    // Remove the assignment
    await prisma.userProperty.delete({
      where: {
        userId_propertyId: {
          userId,
          propertyId,
        },
      },
    });

    res.json({
      success: true,
      data: {
        ...targetUser,
        assignedProperties: [],
      },
      message: 'User removed from property successfully',
    });
  })
);

export default router;

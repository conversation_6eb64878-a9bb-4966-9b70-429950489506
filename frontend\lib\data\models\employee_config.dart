import 'package:json_annotation/json_annotation.dart';

part 'employee_config.g.dart';

@JsonSerializable()
class EmployeeDepartment {
  final String id;
  final String name;
  final String code;
  final String? description;
  final bool isActive;
  final int employeeCount;
  final String createdAt;
  final String updatedAt;

  const EmployeeDepartment({
    required this.id,
    required this.name,
    required this.code,
    this.description,
    required this.isActive,
    required this.employeeCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeDepartment.fromJson(Map<String, dynamic> json) => _$EmployeeDepartmentFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeDepartmentToJson(this);

  EmployeeDepartment copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    bool? isActive,
    int? employeeCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return EmployeeDepartment(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      employeeCount: employeeCount ?? this.employeeCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
}

@JsonSerializable()
class EmployeeStatus {
  final String id;
  final String name;
  final String code;
  final String? description;
  final bool isActive;
  final int employeeCount;
  final String createdAt;
  final String updatedAt;

  const EmployeeStatus({
    required this.id,
    required this.name,
    required this.code,
    this.description,
    required this.isActive,
    required this.employeeCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeStatus.fromJson(Map<String, dynamic> json) => _$EmployeeStatusFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeStatusToJson(this);

  EmployeeStatus copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    bool? isActive,
    int? employeeCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return EmployeeStatus(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      employeeCount: employeeCount ?? this.employeeCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
}

@JsonSerializable()
class EmployeeRole {
  final String id;
  final String name;
  final String code;
  final String? description;
  final List<String> permissions;
  final bool isActive;
  final int employeeCount;
  final String createdAt;
  final String updatedAt;

  const EmployeeRole({
    required this.id,
    required this.name,
    required this.code,
    this.description,
    required this.permissions,
    required this.isActive,
    required this.employeeCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeRole.fromJson(Map<String, dynamic> json) => _$EmployeeRoleFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeRoleToJson(this);

  EmployeeRole copyWith({
    String? id,
    String? name,
    String? code,
    String? description,
    List<String>? permissions,
    bool? isActive,
    int? employeeCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return EmployeeRole(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      employeeCount: employeeCount ?? this.employeeCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool hasPermission(String permission) => permissions.contains(permission);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
}

@JsonSerializable()
class EmployeeShift {
  final String id;
  final String name;
  final String startTime;
  final String endTime;
  final int durationHours;
  final List<String> workDays;
  final bool isActive;
  final int employeeCount;
  final String createdAt;
  final String updatedAt;

  const EmployeeShift({
    required this.id,
    required this.name,
    required this.startTime,
    required this.endTime,
    required this.durationHours,
    required this.workDays,
    required this.isActive,
    required this.employeeCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EmployeeShift.fromJson(Map<String, dynamic> json) => _$EmployeeShiftFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeShiftToJson(this);

  EmployeeShift copyWith({
    String? id,
    String? name,
    String? startTime,
    String? endTime,
    int? durationHours,
    List<String>? workDays,
    bool? isActive,
    int? employeeCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return EmployeeShift(
      id: id ?? this.id,
      name: name ?? this.name,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      durationHours: durationHours ?? this.durationHours,
      workDays: workDays ?? this.workDays,
      isActive: isActive ?? this.isActive,
      employeeCount: employeeCount ?? this.employeeCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool isWorkDay(String day) => workDays.contains(day.toUpperCase());
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
  
  String get workDaysFormatted => workDays.join(', ');
  String get scheduleFormatted => '$startTime - $endTime';
}

@JsonSerializable()
class EmployeeConfigSummary {
  final int totalDepartments;
  final int activeDepartments;
  final int totalStatuses;
  final int activeStatuses;
  final int totalRoles;
  final int activeRoles;
  final int totalShifts;
  final int activeShifts;
  final int totalEmployees;
  final int activeEmployees;

  const EmployeeConfigSummary({
    required this.totalDepartments,
    required this.activeDepartments,
    required this.totalStatuses,
    required this.activeStatuses,
    required this.totalRoles,
    required this.activeRoles,
    required this.totalShifts,
    required this.activeShifts,
    required this.totalEmployees,
    required this.activeEmployees,
  });

  factory EmployeeConfigSummary.fromJson(Map<String, dynamic> json) => _$EmployeeConfigSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeConfigSummaryToJson(this);

  double get departmentActiveRate => totalDepartments > 0 ? activeDepartments / totalDepartments * 100 : 0;
  double get statusActiveRate => totalStatuses > 0 ? activeStatuses / totalStatuses * 100 : 0;
  double get roleActiveRate => totalRoles > 0 ? activeRoles / totalRoles * 100 : 0;
  double get shiftActiveRate => totalShifts > 0 ? activeShifts / totalShifts * 100 : 0;
  double get employeeActiveRate => totalEmployees > 0 ? activeEmployees / totalEmployees * 100 : 0;
}

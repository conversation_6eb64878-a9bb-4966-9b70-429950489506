import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const createEmployeeSchema = z.object({
  officeId: z.string().uuid(),
  name: z.string().min(1, 'Name is required'),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  employeeId: z.string().min(1, 'Employee ID is required'),
  designation: z.string().min(1, 'Designation is required'),
  departmentId: z.string().optional(),
  joinDate: z.string().datetime(),
});

const updateEmployeeSchema = z.object({
  name: z.string().min(1).optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  designation: z.string().min(1).optional(),
  departmentId: z.string().optional(),
  isActive: z.boolean().optional(),
});

// Get all employees with filtering and pagination
export const getEmployees = async (req: Request, res: Response) => {
  try {
    const {
      officeId,
      department,
      isActive,
      search,
      page = '1',
      limit = '20',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const where: any = {};

    // Office-based access control
    if (officeId) {
      where.officeId = officeId as string;
    }

    if (department) {
      where.departmentId = department as string;
    }

    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    // Text search
    if (search) {
      where.OR = [
        { name: { contains: search as string, mode: 'insensitive' } },
        { email: { contains: search as string, mode: 'insensitive' } },
        { employeeId: { contains: search as string, mode: 'insensitive' } },
        { designation: { contains: search as string, mode: 'insensitive' } },
      ];
    }

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Sort
    const orderBy: any = {};
    orderBy[sortBy as string] = sortOrder === 'desc' ? 'desc' : 'asc';

    // Execute query
    const [employees, total] = await Promise.all([
      prisma.employee.findMany({
        where,
        include: {
          office: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          attendanceRecords: {
            where: {
              date: {
                gte: new Date(new Date().setDate(new Date().getDate() - 30)),
              },
            },
            select: {
              date: true,
              status: true,
            },
          },
        },
        orderBy,
        skip,
        take: limitNum,
      }),
      prisma.employee.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrevious = pageNum > 1;

    res.json({
      success: true,
      data: {
        data: employees,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages,
        hasNext,
        hasPrevious,
      },
    });
  } catch (error) {
    console.error('Error fetching employees:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employees',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Get single employee with details
export const getEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const employee = await prisma.employee.findUnique({
      where: { id },
      include: {
        office: {
          select: {
            id: true,
            name: true,
            type: true,
            address: true,
          },
        },
        attendanceRecords: {
          orderBy: { date: 'desc' },
          take: 30, // Last 30 days
          select: {
            date: true,
            status: true,
            checkInTime: true,
            checkOutTime: true,
            hoursWorked: true,
            notes: true,
          },
        },
      },
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found',
      });
    }

    res.json({
      success: true,
      data: employee,
    });
  } catch (error) {
    console.error('Error fetching employee:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employee',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Create new employee
export const createEmployee = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validatedData = createEmployeeSchema.parse(req.body);

    // Check if employee ID already exists
    const existingEmployee = await prisma.employee.findUnique({
      where: { employeeId: validatedData.employeeId },
    });

    if (existingEmployee) {
      return res.status(400).json({
        success: false,
        message: 'Employee ID already exists',
      });
    }

    // Check if office exists
    const office = await prisma.office.findUnique({
      where: { id: validatedData.officeId },
    });

    if (!office) {
      return res.status(400).json({
        success: false,
        message: 'Office not found',
      });
    }

    // Create the employee
    const employee = await prisma.employee.create({
      data: {
        ...validatedData,
        joinDate: new Date(validatedData.joinDate),
      },
      include: {
        office: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: employee,
      message: 'Employee created successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors,
      });
    }

    console.error('Error creating employee:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create employee',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Update employee
export const updateEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    // Validate request body
    const validatedData = updateEmployeeSchema.parse(req.body);

    // Check if employee exists
    const existingEmployee = await prisma.employee.findUnique({
      where: { id },
    });

    if (!existingEmployee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found',
      });
    }

    // Update the employee
    const employee = await prisma.employee.update({
      where: { id },
      data: validatedData,
      include: {
        office: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: employee,
      message: 'Employee updated successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors,
      });
    }

    console.error('Error updating employee:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update employee',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Delete employee
export const deleteEmployee = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Check if employee exists
    const existingEmployee = await prisma.employee.findUnique({
      where: { id },
    });

    if (!existingEmployee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found',
      });
    }

    // Soft delete by setting isActive to false
    await prisma.employee.update({
      where: { id },
      data: { isActive: false },
    });

    res.json({
      success: true,
      message: 'Employee deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting employee:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete employee',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Get employee statistics
export const getEmployeeStatistics = async (req: Request, res: Response) => {
  try {
    const { officeId, department } = req.query;

    // Build filter
    const where: any = {};
    if (officeId) where.officeId = officeId as string;
    if (department) where.departmentId = department as string;

    // Get statistics
    const [
      totalEmployees,
      activeEmployees,
      departmentStats,
      recentJoins,
    ] = await Promise.all([
      prisma.employee.count({ where }),
      prisma.employee.count({ where: { ...where, isActive: true } }),
      prisma.employee.groupBy({
        by: ['departmentId'],
        where,
        _count: {
          _all: true,
        },
      }),
      prisma.employee.findMany({
        where: {
          ...where,
          joinDate: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
        select: {
          id: true,
          name: true,
          joinDate: true,
          departmentId: true,
        },
        orderBy: { joinDate: 'desc' },
        take: 10,
      }),
    ]);

    const statistics = {
      totalEmployees,
      activeEmployees,
      inactiveEmployees: totalEmployees - activeEmployees,
      departmentBreakdown: departmentStats.map(stat => ({
        department: stat.departmentId || 'Unassigned',
        count: stat._count._all,
      })),
      recentJoins,
    };

    res.json({
      success: true,
      data: statistics,
    });
  } catch (error) {
    console.error('Error fetching employee statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employee statistics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Get employee attendance
export const getEmployeeAttendance = async (req: Request, res: Response) => {
  try {
    const {
      employeeId,
      officeId,
      fromDate,
      toDate,
      page = '1',
      limit = '30',
    } = req.query;

    // Build filter
    const where: any = {};
    if (employeeId) where.employeeId = employeeId as string;
    if (officeId) where.officeId = officeId as string;

    // Date range filter
    if (fromDate || toDate) {
      where.date = {};
      if (fromDate) where.date.gte = new Date(fromDate as string);
      if (toDate) where.date.lte = new Date(toDate as string);
    }

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Execute query
    const [attendance, total] = await Promise.all([
      prisma.attendanceRecord.findMany({
        where,
        include: {
          employee: {
            select: {
              id: true,
              name: true,
              employeeId: true,
              departmentId: true,
            },
          },
          office: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { date: 'desc' },
        skip,
        take: limitNum,
      }),
      prisma.attendanceRecord.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNext = pageNum < totalPages;
    const hasPrevious = pageNum > 1;

    res.json({
      success: true,
      data: {
        data: attendance,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages,
        hasNext,
        hasPrevious,
      },
    });
  } catch (error) {
    console.error('Error fetching employee attendance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employee attendance',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

// Submit employee attendance
export const submitAttendance = async (req: Request, res: Response) => {
  try {
    const {
      employeeId,
      officeId,
      date,
      status,
      checkInTime,
      checkOutTime,
      notes,
    } = req.body;

    // Validate required fields
    if (!employeeId || !officeId || !date || !status) {
      return res.status(400).json({
        success: false,
        message: 'Employee ID, Office ID, date, and status are required',
      });
    }

    // Check if attendance already exists for this date
    const existingAttendance = await prisma.attendanceRecord.findUnique({
      where: {
        officeId_employeeId_date: {
          officeId,
          employeeId,
          date: new Date(date),
        },
      },
    });

    if (existingAttendance) {
      return res.status(400).json({
        success: false,
        message: 'Attendance already recorded for this date',
      });
    }

    // Calculate hours worked if both check-in and check-out times are provided
    let hoursWorked = 0;
    if (checkInTime && checkOutTime) {
      const checkIn = new Date(checkInTime);
      const checkOut = new Date(checkOutTime);
      hoursWorked = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
    }

    // Create attendance record
    const attendance = await prisma.attendanceRecord.create({
      data: {
        employeeId,
        officeId,
        date: new Date(date),
        status,
        checkInTime: checkInTime ? new Date(checkInTime) : null,
        checkOutTime: checkOutTime ? new Date(checkOutTime) : null,
        hoursWorked,
        notes,
      },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            employeeId: true,
          },
        },
        office: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: attendance,
      message: 'Attendance submitted successfully',
    });
  } catch (error) {
    console.error('Error submitting attendance:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit attendance',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

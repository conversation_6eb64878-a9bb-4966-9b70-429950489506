import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'unified_system_management_screen.dart';

class SecuritySystemScreen extends ConsumerWidget {
  final String propertyId;
  
  const SecuritySystemScreen({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return UnifiedSystemManagementScreen(
      propertyId: propertyId,
      systemType: 'SECURITY',
      systemName: 'Security Management',
      systemIcon: Icons.security,
      systemColor: Colors.red,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/construction_sites_service.dart';
import '../../../data/models/office.dart';
import '../../../data/models/dashboard.dart' show PropertySummary;
import '../../providers/office_providers.dart';
import '../../providers/properties_providers.dart';
import '../../widgets/status_indicator.dart';
import '../main/main_navigation_screen.dart' hide StatusIndicator;

class OfficeManagementScreen extends ConsumerStatefulWidget {
  const OfficeManagementScreen({super.key});

  @override
  ConsumerState<OfficeManagementScreen> createState() => _OfficeManagementScreenState();
}

class _OfficeManagementScreenState extends ConsumerState<OfficeManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Office Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              // TODO: Show calendar view
            },
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              // TODO: Download reports
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).primaryColor,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(text: 'Office Locations'),
                Tab(text: 'Construction Sites'),
              ],
            ),
          ),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOfficeLocations(),
                _buildConstructionSites(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showQuickActions();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOfficeLocations() {
    return Consumer(
      builder: (context, ref, child) {
        final officePropertiesAsyncValue = ref.watch(propertiesByTypeProvider('OFFICE'));

        return officePropertiesAsyncValue.when(
          data: (properties) => _buildDynamicOfficePropertiesList(properties),
          loading: () => _buildOfficesLoadingState(),
          error: (error, stack) => _buildOfficesErrorState(error),
        );
      },
    );
  }

  Widget _buildDynamicOfficePropertiesList(List<PropertySummary> properties) {
    if (properties.isEmpty) {
      return _buildEmptyOfficePropertiesState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(propertiesByTypeProvider('OFFICE'));
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: properties.length,
        itemBuilder: (context, index) {
          final property = properties[index];
          return _buildOfficePropertyCard(property);
        },
      ),
    );
  }

  Widget _buildDynamicConstructionPropertiesList(List<PropertySummary> properties) {
    if (properties.isEmpty) {
      return _buildEmptyConstructionPropertiesState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(propertiesByTypeProvider('CONSTRUCTION'));
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: properties.length,
        itemBuilder: (context, index) {
          final property = properties[index];
          return _buildConstructionPropertyCard(property);
        },
      ),
    );
  }

  Widget _buildDynamicOfficesList(List<Office> offices) {
    if (offices.isEmpty) {
      return _buildEmptyOfficesState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(officesProvider);
        ref.invalidate(allOfficesStatisticsProvider);
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: offices.length,
        itemBuilder: (context, index) {
          final office = offices[index];
          return _buildDynamicOfficeCard(office);
        },
      ),
    );
  }

  Widget _buildOfficesLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) => _buildOfficeCardSkeleton(),
    );
  }

  Widget _buildOfficesErrorState(Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load offices',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your connection and try again',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                ref.invalidate(officesProvider);
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyOfficePropertiesState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'No office properties found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Office properties will appear here when added',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyConstructionPropertiesState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'No construction sites found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Construction sites will appear here when added',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyOfficesState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 64,
              color: Colors.grey.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'No offices found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Offices will appear here when added',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // TODO: Uncomment when static office cards are needed
  // Widget _buildOfficeCard(Map<String, dynamic> office) {
  //   Color statusColor = office['status'] == 'operational'
  //       ? AppTheme.successColor
  //       : AppTheme.warningColor;

  //   return Card(
  //     margin: const EdgeInsets.only(bottom: 16),
  //     elevation: 2,
  //     shape: RoundedRectangleBorder(
  //       borderRadius: BorderRadius.circular(12),
  //     ),
  //     child: InkWell(
  //       borderRadius: BorderRadius.circular(12),
  //       onTap: () {
  //         _showOfficeDetail(office);
  //       },
  //       child: Padding(
  //         padding: const EdgeInsets.all(16),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             // Office Header
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 Expanded(
  //                   child: Column(
  //                     crossAxisAlignment: CrossAxisAlignment.start,
  //                     children: [
  //                       Text(
  //                         office['name'],
  //                         style: Theme.of(context).textTheme.titleMedium?.copyWith(
  //                           fontWeight: FontWeight.w600,
  //                         ),
  //                       ),
  //                       Text(
  //                         office['address'],
  //                         style: Theme.of(context).textTheme.bodySmall?.copyWith(
  //                           color: Colors.grey[600],
  //                         ),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //                 StatusIndicator(status: office['status'], showLabel: true),
  //               ],
  //             ),

  //             const SizedBox(height: 16),

  //             // Attendance Stats
  //             Row(
  //               children: [
  //                 Expanded(
  //                   child: _buildOfficeStatCard(
  //                     'Total Employees',
  //                     office['employees'].toString(),
  //                     Icons.people,
  //                     AppTheme.infoColor,
  //                   ),
  //                 ),
  //                 const SizedBox(width: 12),
  //                 Expanded(
  //                   child: _buildOfficeStatCard(
  //                     'Present Today',
  //                     office['presentToday'].toString(),
  //                     Icons.check_circle,
  //                     AppTheme.successColor,
  //                   ),
  //                 ),
  //               ],
  //             ),

  //             const SizedBox(height: 12),

  //             // Attendance Rate
  //             Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                   children: [
  //                     Text(
  //                       'Attendance Rate',
  //                       style: Theme.of(context).textTheme.bodyMedium,
  //                     ),
  //                     Text(
  //                       '${office['attendanceRate']}%',
  //                       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
  //                         fontWeight: FontWeight.w600,
  //                         color: statusColor,
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //                 const SizedBox(height: 4),
  //                 LinearProgressIndicator(
  //                   value: office['attendanceRate'] / 100,
  //                   backgroundColor: Colors.grey[300],
  //                   valueColor: AlwaysStoppedAnimation<Color>(statusColor),
  //                 ),
  //               ],
  //             ),

  //             const SizedBox(height: 12),

  //             // Quick Actions
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  //               children: [
  //                 _buildQuickActionButton(
  //                   'Attendance',
  //                   Icons.assignment_turned_in,
  //                   () => _navigateToAttendance(office['id']),
  //                 ),
  //                 _buildQuickActionButton(
  //                   'Employees',
  //                   Icons.people_outline,
  //                   () => _navigateToEmployees(office['id']),
  //                 ),
  //                 _buildQuickActionButton(
  //                   'Reports',
  //                   Icons.assessment,
  //                   () => _navigateToReports(office['id']),
  //                 ),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildDynamicOfficeCard(Office office) {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsyncValue = ref.watch(officeStatisticsProvider(office.id));

        return statisticsAsyncValue.when(
          data: (statistics) => _buildOfficeCardWithStats(office, statistics),
          loading: () => _buildOfficeCardSkeleton(),
          error: (error, stack) => _buildOfficeCardWithError(office, error),
        );
      },
    );
  }

  Widget _buildOfficeCardWithStats(Office office, OfficeStatistics statistics) {
    final statusColor = _getOfficeStatusColor(statistics.attendanceRate);
    final statusText = _getOfficeStatusText(statistics.attendanceRate);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          _navigateToOfficeDetail(office.id);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Office Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          office.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          office.address,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        if (office.description?.isNotEmpty == true)
                          Text(
                            office.description!,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[500],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: statusText.toLowerCase()),
                ],
              ),

              const SizedBox(height: 16),

              // Attendance Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Employees',
                      statistics.totalEmployees.toString(),
                      Icons.people,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      statistics.presentToday.toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Attendance Rate
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Attendance Rate',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${statistics.attendanceRate.toStringAsFixed(1)}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: statistics.attendanceRate / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),

              if (statistics.avgCheckInTime.isNotEmpty || statistics.avgCheckOutTime.isNotEmpty) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    if (statistics.avgCheckInTime.isNotEmpty)
                      Expanded(
                        child: _buildTimeStatCard(
                          'Avg Check-in',
                          statistics.avgCheckInTime,
                          Icons.login,
                        ),
                      ),
                    if (statistics.avgCheckInTime.isNotEmpty && statistics.avgCheckOutTime.isNotEmpty)
                      const SizedBox(width: 12),
                    if (statistics.avgCheckOutTime.isNotEmpty)
                      Expanded(
                        child: _buildTimeStatCard(
                          'Avg Check-out',
                          statistics.avgCheckOutTime,
                          Icons.logout,
                        ),
                      ),
                  ],
                ),
              ],

              const SizedBox(height: 12),

              // Quick Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickActionButton(
                    'Attendance',
                    Icons.assignment_turned_in,
                    () => _navigateToAttendance(office.id),
                  ),
                  _buildQuickActionButton(
                    'Employees',
                    Icons.people_outline,
                    () => _navigateToEmployees(office.id),
                  ),
                  _buildQuickActionButton(
                    'Reports',
                    Icons.assessment,
                    () => _navigateToReports(office.id),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfficeStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header skeleton
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 18,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        height: 14,
                        width: 150,
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 80,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Stats skeleton
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Progress bar skeleton
            Container(
              height: 4,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfficeCardWithError(Office office, Object error) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Office Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        office.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        office.address,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                StatusIndicator(status: 'error'),
              ],
            ),

            const SizedBox(height: 16),

            // Error message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Failed to load office statistics',
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      ref.invalidate(officeStatisticsProvider(office.id));
                    },
                    child: Text('Retry', style: TextStyle(fontSize: 12)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeStatCard(String title, String time, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 16),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                time,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getOfficeStatusColor(double attendanceRate) {
    if (attendanceRate >= 90) {
      return AppTheme.successColor;
    } else if (attendanceRate >= 75) {
      return AppTheme.warningColor;
    } else {
      return AppTheme.errorColor;
    }
  }

  String _getOfficeStatusText(double attendanceRate) {
    if (attendanceRate >= 90) {
      return 'OPERATIONAL';
    } else if (attendanceRate >= 75) {
      return 'WARNING';
    } else {
      return 'CRITICAL';
    }
  }

  Widget _buildOfficePropertyCard(PropertySummary property) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          context.go('/properties/${property.id}');
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          property.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Office Property',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: property.status.toLowerCase()),
                ],
              ),

              const SizedBox(height: 16),

              // Property Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Health Score',
                      '${property.healthScore.toStringAsFixed(1)}%',
                      Icons.health_and_safety,
                      AppTheme.successColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Alerts',
                      property.alertCount.toString(),
                      Icons.warning,
                      property.alertCount > 0 ? AppTheme.warningColor : AppTheme.successColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Last Updated
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.business,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Office Property',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  Text(
                    'Updated ${_formatDateTime(property.lastUpdate)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConstructionPropertyCard(PropertySummary property) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          context.go('/properties/${property.id}');
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          property.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'Health: ${property.healthScore.toStringAsFixed(1)}% • ${property.alertCount} alerts',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: property.status.toLowerCase(), showText: true),
                ],
              ),

              const SizedBox(height: 16),

              // Status and Last Updated
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.construction,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Construction Site',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  Text(
                    'Updated ${_formatDateTime(property.lastUpdate)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConstructionSites() {
    return Consumer(
      builder: (context, ref, child) {
        final constructionPropertiesAsyncValue = ref.watch(propertiesByTypeProvider('CONSTRUCTION'));

        return constructionPropertiesAsyncValue.when(
          data: (properties) => _buildDynamicConstructionPropertiesList(properties),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error loading construction sites: $error'),
                ElevatedButton(
                  onPressed: () => ref.invalidate(propertiesByTypeProvider('CONSTRUCTION')),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildConstructionSiteCardFromModel(ConstructionSite site) {
    Color statusColor = site.status == 'operational'
        ? AppTheme.successColor
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          // TODO: Implement site detail when needed
          // _showSiteDetail(site);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          site.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          site.location,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: site.status),
                ],
              ),

              const SizedBox(height: 16),

              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Project Progress',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${site.progress.toStringAsFixed(0)}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: site.progress / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Worker Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Workers',
                      site.workers.toString(),
                      Icons.engineering,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      site.presentToday.toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConstructionSiteCard(Map<String, dynamic> site) {
    Color statusColor = site['status'] == 'operational' 
        ? AppTheme.successColor 
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          // TODO: Implement site detail when needed
          // _showSiteDetail(site);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Site Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          site['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          site['location'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  StatusIndicator(status: site['status']),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Progress Bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Project Progress',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        '${site['progress']}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: site['progress'] / 100,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Worker Stats
              Row(
                children: [
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Total Workers',
                      site['workers'].toString(),
                      Icons.engineering,
                      AppTheme.infoColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildOfficeStatCard(
                      'Present Today',
                      site['presentToday'].toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // TODO: Uncomment when static office detail is needed
  // void _showOfficeDetail(Map<String, dynamic> office) {
  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     shape: const RoundedRectangleBorder(
  //       borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //     ),
  //     builder: (context) => DraggableScrollableSheet(
  //       initialChildSize: 0.7,
  //       maxChildSize: 0.9,
  //       minChildSize: 0.5,
  //       builder: (context, scrollController) => Container(
  //         padding: const EdgeInsets.all(16),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             // Handle
  //             Center(
  //               child: Container(
  //                 width: 40,
  //                 height: 4,
  //                 decoration: BoxDecoration(
  //                   color: Colors.grey[300],
  //                   borderRadius: BorderRadius.circular(2),
  //                 ),
  //               ),
  //             ),

  //             const SizedBox(height: 16),

  //             Text(
  //               office['name'],
  //               style: Theme.of(context).textTheme.titleLarge?.copyWith(
  //                 fontWeight: FontWeight.bold,
  //               ),
  //             ),

  //             const SizedBox(height: 16),

  //             // Quick actions for office detail
  //             Text(
  //               'Quick Actions',
  //               style: Theme.of(context).textTheme.titleMedium?.copyWith(
  //                 fontWeight: FontWeight.w600,
  //               ),
  //             ),

  //             const SizedBox(height: 12),

  //             GridView.count(
  //               shrinkWrap: true,
  //               crossAxisCount: 2,
  //               crossAxisSpacing: 12,
  //               mainAxisSpacing: 12,
  //               childAspectRatio: 2,
  //               children: [
  //                 _buildDetailActionCard('Submit Attendance', Icons.assignment_turned_in),
  //                 _buildDetailActionCard('Add/Remove Members', Icons.people),
  //                 _buildDetailActionCard('Download Reports', Icons.download),
  //                 _buildDetailActionCard('View Analytics', Icons.analytics),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  void _showDynamicOfficeDetail(Office office, OfficeStatistics statistics) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.6,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Office Header
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            office.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            office.address,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          if (office.description?.isNotEmpty == true) ...[
                            const SizedBox(height: 4),
                            Text(
                              office.description!,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    StatusIndicator(
                      status: _getOfficeStatusText(statistics.attendanceRate).toLowerCase(),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Statistics Overview
                Text(
                  'Today\'s Statistics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: _buildOfficeStatCard(
                        'Total Employees',
                        statistics.totalEmployees.toString(),
                        Icons.people,
                        AppTheme.infoColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildOfficeStatCard(
                        'Present Today',
                        statistics.presentToday.toString(),
                        Icons.check_circle,
                        AppTheme.successColor,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Attendance Rate
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getOfficeStatusColor(statistics.attendanceRate).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getOfficeStatusColor(statistics.attendanceRate).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Attendance Rate',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${statistics.attendanceRate.toStringAsFixed(1)}%',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _getOfficeStatusColor(statistics.attendanceRate),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: statistics.attendanceRate / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getOfficeStatusColor(statistics.attendanceRate),
                        ),
                      ),
                    ],
                  ),
                ),

                if (statistics.avgCheckInTime.isNotEmpty || statistics.avgCheckOutTime.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Average Times',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (statistics.avgCheckInTime.isNotEmpty)
                        Expanded(
                          child: _buildTimeStatCard(
                            'Avg Check-in',
                            statistics.avgCheckInTime,
                            Icons.login,
                          ),
                        ),
                      if (statistics.avgCheckInTime.isNotEmpty && statistics.avgCheckOutTime.isNotEmpty)
                        const SizedBox(width: 12),
                      if (statistics.avgCheckOutTime.isNotEmpty)
                        Expanded(
                          child: _buildTimeStatCard(
                            'Avg Check-out',
                            statistics.avgCheckOutTime,
                            Icons.logout,
                          ),
                        ),
                    ],
                  ),
                ],

                const SizedBox(height: 24),

                // Quick Actions
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 12),

                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 2,
                  children: [
                    _buildDetailActionCard('Submit Attendance', Icons.assignment_turned_in),
                    _buildDetailActionCard('View Attendance', Icons.people),
                    _buildDetailActionCard('Download Reports', Icons.download),
                    _buildDetailActionCard('View Analytics', Icons.analytics),
                  ],
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailActionCard(String title, IconData icon) {
    return Card(
      child: InkWell(
        onTap: () {
          Navigator.pop(context);
          // TODO: Navigate to specific action
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // TODO: Uncomment when static site detail is needed
  // void _showSiteDetail(Map<String, dynamic> site) {
  //   // Similar to office detail but for construction sites
  //   _showOfficeDetail(site);
  // }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.assignment_turned_in),
              title: const Text('Submit Attendance'),
              onTap: () {
                Navigator.pop(context);
                _showAttendanceSubmissionDialog(context);
              },
            ),

            ListTile(
              leading: const Icon(Icons.people_alt),
              title: const Text('Add Employee'),
              onTap: () {
                Navigator.pop(context);
                _showAddEmployeeDialog(context);
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Download Reports'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Navigate to reports
              },
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAttendance(String officeId) {
    context.go('/office/attendance?officeId=$officeId');
  }

  void _navigateToEmployees(String officeId) {
    context.go('/office/employees?officeId=$officeId');
  }

  void _navigateToReports(String officeId) {
    context.go('/office/reports?officeId=$officeId');
  }

  void _navigateToOfficeDetail(String officeId) {
    context.go('/office/$officeId');
  }

  void _showAttendanceSubmissionDialog(BuildContext context) {
    final employeeController = TextEditingController();
    final notesController = TextEditingController();
    String selectedStatus = 'Present';
    DateTime selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Submit Attendance'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Employee Selection
                TextFormField(
                  controller: employeeController,
                  decoration: const InputDecoration(
                    labelText: 'Employee Name',
                    hintText: 'Enter employee name',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Date Selection
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('Date'),
                  subtitle: Text(
                    '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime.now().subtract(const Duration(days: 30)),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        selectedDate = date;
                      });
                    }
                  },
                ),

                // Status Selection
                const Text('Status:', style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: selectedStatus,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: ['Present', 'Absent', 'Late', 'Half Day'].map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedStatus = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Notes
                TextFormField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    hintText: 'Add any additional notes',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (employeeController.text.trim().isNotEmpty) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Attendance submitted for ${employeeController.text.trim()} - $selectedStatus',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                  // TODO: Implement actual attendance submission API call
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter employee name'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Submit'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddEmployeeDialog(BuildContext context) {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();
    final positionController = TextEditingController();
    String selectedDepartment = 'General';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Employee'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Name
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Full Name',
                    hintText: 'Enter employee name',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Email
                TextFormField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    hintText: 'Enter email address',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Phone
                TextFormField(
                  controller: phoneController,
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(
                    labelText: 'Phone Number',
                    hintText: 'Enter phone number',
                    prefixIcon: Icon(Icons.phone),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Position
                TextFormField(
                  controller: positionController,
                  decoration: const InputDecoration(
                    labelText: 'Position',
                    hintText: 'Enter job position',
                    prefixIcon: Icon(Icons.work),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Department
                const Text('Department:', style: TextStyle(fontWeight: FontWeight.w500)),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: selectedDepartment,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: ['General', 'Maintenance', 'Security', 'Administration', 'Technical'].map((dept) {
                    return DropdownMenuItem(
                      value: dept,
                      child: Text(dept),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedDepartment = value!;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty &&
                    emailController.text.trim().isNotEmpty) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Employee ${nameController.text.trim()} added successfully',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                  // TODO: Implement actual employee creation API call
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please fill in required fields (Name and Email)'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Add Employee'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getPropertyStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'OPERATIONAL':
        return AppTheme.successColor;
      case 'WARNING':
        return AppTheme.warningColor;
      case 'CRITICAL':
        return AppTheme.errorColor;
      case 'OFFLINE':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return 'Recently';
    }
  }
}

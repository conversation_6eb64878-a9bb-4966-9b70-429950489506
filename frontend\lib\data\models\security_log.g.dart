// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'security_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SecurityLog _$SecurityLogFromJson(Map<String, dynamic> json) => SecurityLog(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      activity: json['activity'] as String,
      location: json['location'] as String,
      guardName: json['guardName'] as String,
      status: json['status'] as String,
      notes: json['notes'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$SecurityLogToJson(SecurityLog instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'activity': instance.activity,
      'location': instance.location,
      'guardName': instance.guardName,
      'status': instance.status,
      'notes': instance.notes,
      'timestamp': instance.timestamp.toIso8601String(),
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

PaginatedSecurityLogs _$PaginatedSecurityLogsFromJson(
        Map<String, dynamic> json) =>
    PaginatedSecurityLogs(
      data: (json['data'] as List<dynamic>)
          .map((e) => SecurityLog.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
    );

Map<String, dynamic> _$PaginatedSecurityLogsToJson(
        PaginatedSecurityLogs instance) =>
    <String, dynamic>{
      'data': instance.data,
      'total': instance.total,
      'page': instance.page,
      'limit': instance.limit,
      'totalPages': instance.totalPages,
    };

SecurityIncident _$SecurityIncidentFromJson(Map<String, dynamic> json) =>
    SecurityIncident(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      severity: json['severity'] as String,
      status: json['status'] as String,
      reportedBy: json['reportedBy'] as String,
      assignedTo: json['assignedTo'] as String?,
      location: json['location'] as String?,
      incidentDate: DateTime.parse(json['incidentDate'] as String),
      resolvedDate: json['resolvedDate'] == null
          ? null
          : DateTime.parse(json['resolvedDate'] as String),
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$SecurityIncidentToJson(SecurityIncident instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'title': instance.title,
      'description': instance.description,
      'severity': instance.severity,
      'status': instance.status,
      'reportedBy': instance.reportedBy,
      'assignedTo': instance.assignedTo,
      'location': instance.location,
      'incidentDate': instance.incidentDate.toIso8601String(),
      'resolvedDate': instance.resolvedDate?.toIso8601String(),
      'attachments': instance.attachments,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

PaginatedSecurityIncidents _$PaginatedSecurityIncidentsFromJson(
        Map<String, dynamic> json) =>
    PaginatedSecurityIncidents(
      data: (json['data'] as List<dynamic>)
          .map((e) => SecurityIncident.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
    );

Map<String, dynamic> _$PaginatedSecurityIncidentsToJson(
        PaginatedSecurityIncidents instance) =>
    <String, dynamic>{
      'data': instance.data,
      'total': instance.total,
      'page': instance.page,
      'limit': instance.limit,
      'totalPages': instance.totalPages,
    };

SecurityAnalytics _$SecurityAnalyticsFromJson(Map<String, dynamic> json) =>
    SecurityAnalytics(
      overview:
          SecurityOverview.fromJson(json['overview'] as Map<String, dynamic>),
      trends: (json['trends'] as List<dynamic>)
          .map((e) => SecurityTrend.fromJson(e as Map<String, dynamic>))
          .toList(),
      incidentsBySeverity:
          Map<String, int>.from(json['incidentsBySeverity'] as Map),
      incidentsByLocation:
          Map<String, int>.from(json['incidentsByLocation'] as Map),
      timeRange: TimeRange.fromJson(json['timeRange'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SecurityAnalyticsToJson(SecurityAnalytics instance) =>
    <String, dynamic>{
      'overview': instance.overview,
      'trends': instance.trends,
      'incidentsBySeverity': instance.incidentsBySeverity,
      'incidentsByLocation': instance.incidentsByLocation,
      'timeRange': instance.timeRange,
    };

SecurityOverview _$SecurityOverviewFromJson(Map<String, dynamic> json) =>
    SecurityOverview(
      totalIncidents: (json['totalIncidents'] as num).toInt(),
      openIncidents: (json['openIncidents'] as num).toInt(),
      resolvedIncidents: (json['resolvedIncidents'] as num).toInt(),
      criticalIncidents: (json['criticalIncidents'] as num).toInt(),
      avgResolutionTime: (json['avgResolutionTime'] as num).toDouble(),
      resolutionRate: (json['resolutionRate'] as num).toDouble(),
    );

Map<String, dynamic> _$SecurityOverviewToJson(SecurityOverview instance) =>
    <String, dynamic>{
      'totalIncidents': instance.totalIncidents,
      'openIncidents': instance.openIncidents,
      'resolvedIncidents': instance.resolvedIncidents,
      'criticalIncidents': instance.criticalIncidents,
      'avgResolutionTime': instance.avgResolutionTime,
      'resolutionRate': instance.resolutionRate,
    };

SecurityTrend _$SecurityTrendFromJson(Map<String, dynamic> json) =>
    SecurityTrend(
      date: json['date'] as String,
      incidents: (json['incidents'] as num).toInt(),
      resolved: (json['resolved'] as num).toInt(),
      critical: (json['critical'] as num).toInt(),
    );

Map<String, dynamic> _$SecurityTrendToJson(SecurityTrend instance) =>
    <String, dynamic>{
      'date': instance.date,
      'incidents': instance.incidents,
      'resolved': instance.resolved,
      'critical': instance.critical,
    };

TimeRange _$TimeRangeFromJson(Map<String, dynamic> json) => TimeRange(
      period: json['period'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$TimeRangeToJson(TimeRange instance) => <String, dynamic>{
      'period': instance.period,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
    };

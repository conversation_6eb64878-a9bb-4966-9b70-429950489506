import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const createSecurityLogSchema = z.object({
  propertyId: z.string(),
  activity: z.string().min(1),
  location: z.string().min(1),
  guardName: z.string().min(1),
  status: z.enum(['NORMAL', 'LOGGED', 'COMPLETED', 'VERIFIED', 'ALERT']),
  notes: z.string().optional(),
  timestamp: z.string().optional(),
});

const updateSecurityLogSchema = z.object({
  activity: z.string().min(1).optional(),
  location: z.string().min(1).optional(),
  guardName: z.string().min(1).optional(),
  status: z.enum(['NORMAL', 'LOGGED', 'COMPLETED', 'VERIFIED', 'ALERT']).optional(),
  notes: z.string().optional(),
});

const securityLogQuerySchema = z.object({
  propertyId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  status: z.string().optional(),
  guardName: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
});

// Get security logs
export const getSecurityLogs = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;
    const query = securityLogQuerySchema.parse(req.query);

    const page = parseInt(query.page || '1');
    const limit = parseInt(query.limit || '20');
    const skip = (page - 1) * limit;

    // Build property filter
    let propertyFilter = {};
    if (query.propertyId) {
      propertyFilter = { propertyId: query.propertyId };
    } else if (userRole !== 'SUPER_ADMIN') {
      const userProperties = await prisma.userProperty.findMany({
        where: { userId },
        select: { propertyId: true }
      });
      
      if (userProperties.length === 0) {
        return res.json({
          success: true,
          data: [],
          pagination: { page, limit, total: 0, totalPages: 0 }
        });
      }
      
      propertyFilter = {
        propertyId: { in: userProperties.map(up => up.propertyId) }
      };
    }

    // Build additional filters
    const filters: any = { ...propertyFilter };

    if (query.startDate || query.endDate) {
      filters.timestamp = {};
      if (query.startDate) filters.timestamp.gte = new Date(query.startDate);
      if (query.endDate) filters.timestamp.lte = new Date(query.endDate);
    }

    if (query.status) {
      filters.status = query.status;
    }

    if (query.guardName) {
      filters.guardName = { contains: query.guardName, mode: 'insensitive' };
    }

    // Get total count
    const total = await prisma.securityLog.count({ where: filters });

    // Get security logs
    const securityLogs = await prisma.securityLog.findMany({
      where: filters,
      include: {
        property: {
          select: { id: true, name: true }
        }
      },
      orderBy: { timestamp: 'desc' },
      skip,
      take: limit
    });

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: securityLogs,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error fetching security logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch security logs'
    });
  }
};

// Get security log by ID
export const getSecurityLogById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    const securityLog = await prisma.securityLog.findUnique({
      where: { id },
      include: {
        property: {
          select: { id: true, name: true }
        }
      }
    });

    if (!securityLog) {
      return res.status(404).json({
        success: false,
        message: 'Security log not found'
      });
    }

    // Check property access
    if (userRole !== 'SUPER_ADMIN') {
      const userProperty = await prisma.userProperty.findFirst({
        where: { userId, propertyId: securityLog.propertyId }
      });
      
      if (!userProperty) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this security log'
        });
      }
    }

    res.json({
      success: true,
      data: securityLog
    });
  } catch (error) {
    console.error('Error fetching security log:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch security log'
    });
  }
};

// Create security log
export const createSecurityLog = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;
    const validatedData = createSecurityLogSchema.parse(req.body);

    // Check property access
    if (userRole !== 'SUPER_ADMIN') {
      const userProperty = await prisma.userProperty.findFirst({
        where: { userId, propertyId: validatedData.propertyId }
      });
      
      if (!userProperty) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to create security log for this property'
        });
      }
    }

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: validatedData.propertyId }
    });

    if (!property) {
      return res.status(404).json({
        success: false,
        message: 'Property not found'
      });
    }

    const securityLog = await prisma.securityLog.create({
      data: {
        propertyId: validatedData.propertyId,
        activity: validatedData.activity,
        location: validatedData.location,
        guardName: validatedData.guardName,
        status: validatedData.status,
        notes: validatedData.notes,
        timestamp: validatedData.timestamp ? new Date(validatedData.timestamp) : new Date(),
        createdBy: userId!
      },
      include: {
        property: {
          select: { id: true, name: true }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: securityLog,
      message: 'Security log created successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error creating security log:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create security log'
    });
  }
};

// Update security log
export const updateSecurityLog = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;
    const validatedData = updateSecurityLogSchema.parse(req.body);

    // Check if security log exists
    const existingLog = await prisma.securityLog.findUnique({
      where: { id }
    });

    if (!existingLog) {
      return res.status(404).json({
        success: false,
        message: 'Security log not found'
      });
    }

    // Check property access
    if (userRole !== 'SUPER_ADMIN') {
      const userProperty = await prisma.userProperty.findFirst({
        where: { userId, propertyId: existingLog.propertyId }
      });
      
      if (!userProperty) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to update this security log'
        });
      }
    }

    const updatedLog = await prisma.securityLog.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        property: {
          select: { id: true, name: true }
        }
      }
    });

    res.json({
      success: true,
      data: updatedLog,
      message: 'Security log updated successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error updating security log:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update security log'
    });
  }
};

// Delete security log
export const deleteSecurityLog = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    // Check if security log exists
    const existingLog = await prisma.securityLog.findUnique({
      where: { id }
    });

    if (!existingLog) {
      return res.status(404).json({
        success: false,
        message: 'Security log not found'
      });
    }

    // Check property access
    if (userRole !== 'SUPER_ADMIN') {
      const userProperty = await prisma.userProperty.findFirst({
        where: { userId, propertyId: existingLog.propertyId }
      });
      
      if (!userProperty) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to delete this security log'
        });
      }
    }

    await prisma.securityLog.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Security log deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting security log:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete security log'
    });
  }
};

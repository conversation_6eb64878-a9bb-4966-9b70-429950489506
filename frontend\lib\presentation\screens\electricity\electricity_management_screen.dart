import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/permission_wrapper.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/system.dart';
import '../../providers/system_providers.dart';
import '../../providers/auth_providers.dart';
import '../../widgets/enhanced_error_widget.dart';
import '../../widgets/retry_widget.dart';
import '../../widgets/offline_indicator.dart';
import '../main/main_navigation_screen.dart';

class ElectricityManagementScreen extends ConsumerStatefulWidget {
  final String? propertyId;

  const ElectricityManagementScreen({super.key, this.propertyId});

  @override
  ConsumerState<ElectricityManagementScreen> createState() => _ElectricityManagementScreenState();
}

class _ElectricityManagementScreenState extends ConsumerState<ElectricityManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userAsyncValue = ref.watch(currentUserProvider);

    return userAsyncValue.when(
      data: (user) {
        if (user == null) {
          return Scaffold(
            appBar: CustomAppBar(title: 'Electricity Management'),
            body: const Center(child: Text('Please log in to continue')),
          );
        }

        // Check RBAC permissions
        if (!user.permissions.canManageElectricitySystems) {
          return Scaffold(
            appBar: CustomAppBar(title: 'Electricity Management'),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lock_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Access Denied',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You don\'t have permission to view electricity management data',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          appBar: CustomAppBar(
            title: 'Electricity Management',
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _refreshData,
              ),
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: _showSystemSettings,
              ),
              if (user.permissions.canManageElectricitySystems)
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _showAddSystemDialog,
                ),
            ],
            bottom: TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: AppTheme.primaryColor,
              isScrollable: true,
              tabs: const [
                Tab(text: 'Generator Status'),
                Tab(text: 'Power Analytics'),
                Tab(text: 'Maintenance'),
                Tab(text: 'Load Management'),
              ],
            ),
          ),
          body: Column(
            children: [
              // Offline Indicator
              const OfflineIndicator(),

              // Tab Bar View
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    GeneratorStatusTab(propertyId: widget.propertyId),
                    PowerAnalyticsTab(propertyId: widget.propertyId),
                    ElectricityMaintenanceTab(propertyId: widget.propertyId),
                    LoadManagementTab(propertyId: widget.propertyId),
                  ],
                ),
              ),
            ],
          ),
        );
      },
      loading: () => Scaffold(
        appBar: CustomAppBar(title: 'Electricity Management'),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: CustomAppBar(title: 'Electricity Management'),
        body: Center(child: Text('Error: $error')),
      ),
    );
  }

  void _refreshData() {
    ref.invalidate(electricitySystemsProvider);
    ref.invalidate(powerAnalyticsProvider);
    ref.invalidate(electricityMaintenanceProvider);
    ref.invalidate(loadManagementProvider);
  }

  void _showSystemSettings() {
    showDialog(
      context: context,
      builder: (context) => ElectricitySystemSettingsDialog(propertyId: widget.propertyId),
    );
  }

  void _showAddSystemDialog() {
    showDialog(
      context: context,
      builder: (context) => AddElectricitySystemDialog(propertyId: widget.propertyId),
    );
  }
}

// Generator Status Tab
class GeneratorStatusTab extends ConsumerWidget {
  final String? propertyId;

  const GeneratorStatusTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final electricitySystemsAsyncValue = ref.watch(electricitySystemsProvider(propertyId ?? ''));

    return electricitySystemsAsyncValue.when(
      data: (rawData) {
        // Convert raw data to ElectricitySystemsResponse
        final systems = rawData.map((json) => ElectricitySystem.fromJson(json)).toList();
        final response = ElectricitySystemsResponse(
          systems: systems,
          summary: _calculateSummary(systems),
        );
        return _buildGeneratorStatusContent(context, ref, response);
      },
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildGeneratorStatusContent(BuildContext context, WidgetRef ref, ElectricitySystemsResponse response) {
    if (response.systems.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(electricitySystemsProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            _buildSummaryCards(context, response.summary),
            
            const SizedBox(height: 24),
            
            // Generator Status Cards
            Text(
              'Generator Systems',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...response.systems.map((system) => _buildGeneratorCard(context, system)),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, ElectricitySystemsSummary summary) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Power',
            '${summary.totalPowerConsumption.toStringAsFixed(1)}kW',
            Icons.flash_on,
            AppTheme.primaryColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Active Generators',
            '${summary.activeGenerators}/${summary.totalSystems}',
            Icons.power,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneratorCard(BuildContext context, ElectricitySystem system) {
    final generatorStatusColor = _getGeneratorStatusColor(system.generatorStatus);
    final mainsStatusColor = _getMainsStatusColor(system.mainsPowerStatus);
    final fuelLevelColor = _getFuelLevelColor(system.fuelLevel ?? 0);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    system.systemName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: generatorStatusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: generatorStatusColor.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    system.generatorStatus,
                    style: TextStyle(
                      color: generatorStatusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Status Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Mains Power',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: mainsStatusColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: mainsStatusColor.withValues(alpha: 0.3)),
                        ),
                        child: Text(
                          system.mainsPowerStatus,
                          style: TextStyle(
                            color: mainsStatusColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                if (system.fuelLevel != null) ...[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Fuel Level',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            Text(
                              '${system.fuelLevel!.toStringAsFixed(1)}%',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: fuelLevelColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: system.fuelLevel! / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(fuelLevelColor),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Metrics Row
            Row(
              children: [
                if (system.powerConsumption != null) ...[
                  Icon(Icons.flash_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${system.powerConsumption!.toStringAsFixed(1)} kW',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                
                if (system.voltage != null) ...[
                  Icon(Icons.electrical_services, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${system.voltage!.toStringAsFixed(0)}V',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                
                if (system.loadPercentage != null) ...[
                  Icon(Icons.speed, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${system.loadPercentage!.toStringAsFixed(1)}% Load',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
            
            if (system.needsMaintenance) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Maintenance Required',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (context, index) => _buildGeneratorCardSkeleton(),
    );
  }

  Widget _buildGeneratorCardSkeleton() {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  width: 60,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return RetryWidget(
      onRetry: () async {
        ref.invalidate(electricitySystemsProvider);
      },
      child: const SizedBox.shrink(),
      errorBuilder: (error, retryCount, retry) => EnhancedErrorWidget(
        error: error,
        onRetry: retry,
        title: 'Failed to load electricity systems',
        message: 'Please check your connection and try again.',
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.power_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No electricity systems found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Electricity systems will appear here when configured',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getGeneratorStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ON':
        return Colors.green;
      case 'OFF':
        return Colors.grey;
      case 'STANDBY':
        return Colors.blue;
      case 'MAINTENANCE':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getMainsStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'AVAILABLE':
        return Colors.green;
      case 'UNAVAILABLE':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getFuelLevelColor(double level) {
    if (level < 10) return Colors.red;
    if (level < 30) return Colors.orange;
    return Colors.green;
  }

  ElectricitySystemsSummary _calculateSummary(List<ElectricitySystem> systems) {
    if (systems.isEmpty) {
      return const ElectricitySystemsSummary(
        totalSystems: 0,
        totalPowerConsumption: 0,
        averageFuelLevel: 0,
        activeGenerators: 0,
        mainsAvailable: 0,
        maintenanceRequired: 0,
      );
    }

    final totalSystems = systems.length;
    final totalPowerConsumption = systems
        .where((s) => s.powerConsumption != null)
        .fold(0.0, (sum, s) => sum + s.powerConsumption!);
    final averageFuelLevel = systems
        .where((s) => s.fuelLevel != null)
        .fold(0.0, (sum, s) => sum + s.fuelLevel!) /
        systems.where((s) => s.fuelLevel != null).length;
    final activeGenerators = systems
        .where((s) => s.generatorStatus == 'ON')
        .length;
    final mainsAvailable = systems
        .where((s) => s.mainsPowerStatus == 'AVAILABLE')
        .length;
    final maintenanceRequired = systems
        .where((s) => s.needsMaintenance)
        .length;

    return ElectricitySystemsSummary(
      totalSystems: totalSystems,
      totalPowerConsumption: totalPowerConsumption,
      averageFuelLevel: averageFuelLevel.isNaN ? 0 : averageFuelLevel.round(),
      activeGenerators: activeGenerators,
      mainsAvailable: mainsAvailable,
      maintenanceRequired: maintenanceRequired,
    );
  }
}

// Power Analytics Tab
class PowerAnalyticsTab extends StatelessWidget {
  final String? propertyId;

  const PowerAnalyticsTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Analytics Header
          Row(
            children: [
              const Icon(Icons.analytics, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Power Analytics',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick Stats Cards
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  'Today\'s Consumption',
                  '45.2 kWh',
                  Icons.flash_on,
                  Colors.blue,
                  '+12% from yesterday',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAnalyticsCard(
                  'Monthly Average',
                  '1,234 kWh',
                  Icons.trending_up,
                  Colors.green,
                  'On track',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildAnalyticsCard(
                  'Peak Load',
                  '8.5 kW',
                  Icons.speed,
                  Colors.orange,
                  'At 2:30 PM',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAnalyticsCard(
                  'Cost This Month',
                  '₹12,450',
                  Icons.currency_rupee,
                  Colors.purple,
                  '₹2,100 saved',
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Usage Breakdown
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Usage Breakdown (Last 7 Days)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),

                _buildUsageItem('HVAC Systems', 45, Colors.red),
                _buildUsageItem('Lighting', 25, Colors.orange),
                _buildUsageItem('Kitchen Appliances', 15, Colors.blue),
                _buildUsageItem('Electronics', 10, Colors.green),
                _buildUsageItem('Others', 5, Colors.grey),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Recent Readings
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Recent Meter Readings',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Detailed analytics will be implemented in next phase'),
                          ),
                        );
                      },
                      icon: const Icon(Icons.open_in_new, size: 16),
                      label: const Text('View Details'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Sample readings table
                _buildReadingsTable(),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Export functionality will be implemented in next phase'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.download),
                  label: const Text('Export Data'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report generation will be implemented in next phase'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.assessment),
                  label: const Text('Generate Report'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 10,
              color: color.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageItem(String label, int percentage, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              Text(
                '$percentage%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingsTable() {
    // This should be replaced with API data from electricity readings endpoint
    return Consumer(
      builder: (context, ref, child) {
        final electricitySystemsAsyncValue = ref.watch(electricitySystemsProvider(propertyId ?? ''));

        return electricitySystemsAsyncValue.when(
          data: (rawData) {
            if (rawData.isEmpty) {
              return const Center(child: Text('No electricity data available'));
            }

            // Generate sample readings based on API data
            final systems = rawData.map((json) => ElectricitySystem.fromJson(json)).toList();
            final mainSystem = systems.first;
            final baseConsumption = mainSystem.powerConsumption ?? 45.0;

            final readings = List.generate(7, (index) {
              final date = DateTime.now().subtract(Duration(days: index));
              final consumption = baseConsumption + (index * 2.5) + (DateTime.now().millisecond % 10);
              final totalReading = 12450.0 + (index * consumption);

              return {
                'date': index == 0 ? 'Today' :
                       index == 1 ? 'Yesterday' :
                       '${index} days ago',
                'reading': '${totalReading.toStringAsFixed(0)} kWh',
                'consumption': '${consumption.toStringAsFixed(1)} kWh',
              };
            });

            return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(child: Text('Date', style: TextStyle(fontWeight: FontWeight.w600))),
                Expanded(child: Text('Reading', style: TextStyle(fontWeight: FontWeight.w600))),
                Expanded(child: Text('Daily Usage', style: TextStyle(fontWeight: FontWeight.w600))),
              ],
            ),
          ),

          // Data rows
          ...readings.asMap().entries.map((entry) {
            final index = entry.key;
            final reading = entry.value;
            final isLast = index == readings.length - 1;

            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: isLast ? null : Border(
                  bottom: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: Row(
                children: [
                  Expanded(child: Text(reading['date']!, style: const TextStyle(fontSize: 14))),
                  Expanded(child: Text(reading['reading']!, style: const TextStyle(fontSize: 14))),
                  Expanded(child: Text(reading['consumption']!, style: const TextStyle(fontSize: 14))),
                ],
              ),
            );
          }),
        ],
      ),
    );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Error: $error')),
        );
      },
    );
  }
}

class ElectricityMaintenanceTab extends StatelessWidget {
  final String? propertyId;

  const ElectricityMaintenanceTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Electricity Maintenance Tab - Coming Soon'));
  }
}

class LoadManagementTab extends StatelessWidget {
  final String? propertyId;

  const LoadManagementTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Load Management Tab - Coming Soon'));
  }
}

// Placeholder dialog classes
class ElectricitySystemSettingsDialog extends StatelessWidget {
  final String? propertyId;

  const ElectricitySystemSettingsDialog({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('System Settings'),
      content: const Text('System settings dialog - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

class AddElectricitySystemDialog extends StatelessWidget {
  final String? propertyId;

  const AddElectricitySystemDialog({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add System'),
      content: const Text('Add electricity system dialog - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Add'),
        ),
      ],
    );
  }
}

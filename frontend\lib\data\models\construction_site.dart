import 'package:json_annotation/json_annotation.dart';

part 'construction_site.g.dart';

@JsonSerializable()
class ConstructionSite {
  final String id;
  final String name;
  final String description;
  final String location;
  final String status; // PLANNING, ACTIVE, ON_HOLD, COMPLETED, CANCELLED
  final String priority; // LOW, MEDIUM, HIGH, URGENT
  final String projectManager;
  final String contractor;
  final DateTime startDate;
  final DateTime? expectedEndDate;
  final DateTime? actualEndDate;
  final double budget;
  final double spentAmount;
  final double progressPercentage;
  final List<String> attachments;
  final String createdAt;
  final String updatedAt;

  const ConstructionSite({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.status,
    required this.priority,
    required this.projectManager,
    required this.contractor,
    required this.startDate,
    this.expectedEndDate,
    this.actualEndDate,
    required this.budget,
    required this.spentAmount,
    required this.progressPercentage,
    required this.attachments,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ConstructionSite.fromJson(Map<String, dynamic> json) => _$ConstructionSiteFromJson(json);
  Map<String, dynamic> toJson() => _$ConstructionSiteToJson(this);

  ConstructionSite copyWith({
    String? id,
    String? name,
    String? description,
    String? location,
    String? status,
    String? priority,
    String? projectManager,
    String? contractor,
    DateTime? startDate,
    DateTime? expectedEndDate,
    DateTime? actualEndDate,
    double? budget,
    double? spentAmount,
    double? progressPercentage,
    List<String>? attachments,
    String? createdAt,
    String? updatedAt,
  }) {
    return ConstructionSite(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      projectManager: projectManager ?? this.projectManager,
      contractor: contractor ?? this.contractor,
      startDate: startDate ?? this.startDate,
      expectedEndDate: expectedEndDate ?? this.expectedEndDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      budget: budget ?? this.budget,
      spentAmount: spentAmount ?? this.spentAmount,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isPlanning => status.toUpperCase() == 'PLANNING';
  bool get isActive => status.toUpperCase() == 'ACTIVE';
  bool get isOnHold => status.toUpperCase() == 'ON_HOLD';
  bool get isCompleted => status.toUpperCase() == 'COMPLETED';
  bool get isCancelled => status.toUpperCase() == 'CANCELLED';

  bool get isLowPriority => priority.toUpperCase() == 'LOW';
  bool get isMediumPriority => priority.toUpperCase() == 'MEDIUM';
  bool get isHighPriority => priority.toUpperCase() == 'HIGH';
  bool get isUrgentPriority => priority.toUpperCase() == 'URGENT';

  bool get hasAttachments => attachments.isNotEmpty;
  bool get isOverBudget => spentAmount > budget;
  bool get isOnSchedule => expectedEndDate != null && DateTime.now().isBefore(expectedEndDate!);

  double get budgetUtilization => budget > 0 ? (spentAmount / budget) * 100 : 0;
  double get remainingBudget => budget - spentAmount;
  
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  Duration? get totalDuration => expectedEndDate?.difference(startDate);
  Duration? get elapsedTime => DateTime.now().difference(startDate);
  Duration? get remainingTime => expectedEndDate?.difference(DateTime.now());

  int? get totalDays => totalDuration?.inDays;
  int? get elapsedDays => elapsedTime?.inDays;
  int? get remainingDays => remainingTime?.inDays;

  String get budgetUtilizationFormatted => '${budgetUtilization.toStringAsFixed(1)}%';
  String get progressFormatted => '${progressPercentage.toStringAsFixed(1)}%';
}

@JsonSerializable()
class ConstructionSiteAttendance {
  final String id;
  final String siteId;
  final String workerId;
  final String workerName;
  final String workerRole;
  final DateTime checkInTime;
  final DateTime? checkOutTime;
  final int? hoursWorked;
  final String status; // PRESENT, ABSENT, LATE, EARLY_DEPARTURE
  final String? notes;
  final String date;
  final String createdAt;
  final String updatedAt;

  const ConstructionSiteAttendance({
    required this.id,
    required this.siteId,
    required this.workerId,
    required this.workerName,
    required this.workerRole,
    required this.checkInTime,
    this.checkOutTime,
    this.hoursWorked,
    required this.status,
    this.notes,
    required this.date,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ConstructionSiteAttendance.fromJson(Map<String, dynamic> json) => _$ConstructionSiteAttendanceFromJson(json);
  Map<String, dynamic> toJson() => _$ConstructionSiteAttendanceToJson(this);

  ConstructionSiteAttendance copyWith({
    String? id,
    String? siteId,
    String? workerId,
    String? workerName,
    String? workerRole,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    int? hoursWorked,
    String? status,
    String? notes,
    String? date,
    String? createdAt,
    String? updatedAt,
  }) {
    return ConstructionSiteAttendance(
      id: id ?? this.id,
      siteId: siteId ?? this.siteId,
      workerId: workerId ?? this.workerId,
      workerName: workerName ?? this.workerName,
      workerRole: workerRole ?? this.workerRole,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isPresent => status.toUpperCase() == 'PRESENT';
  bool get isAbsent => status.toUpperCase() == 'ABSENT';
  bool get isLate => status.toUpperCase() == 'LATE';
  bool get isEarlyDeparture => status.toUpperCase() == 'EARLY_DEPARTURE';

  bool get isCheckedOut => checkOutTime != null;
  bool get hasNotes => notes != null && notes!.isNotEmpty;

  DateTime get dateTime => DateTime.parse(date);
  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  Duration? get workDuration => checkOutTime?.difference(checkInTime);
  String get workDurationFormatted {
    if (workDuration == null) return 'In Progress';
    final hours = workDuration!.inHours;
    final minutes = workDuration!.inMinutes % 60;
    return '${hours}h ${minutes}m';
  }

  String get checkInTimeFormatted => '${checkInTime.hour}:${checkInTime.minute.toString().padLeft(2, '0')}';
  String get checkOutTimeFormatted => checkOutTime != null 
      ? '${checkOutTime!.hour}:${checkOutTime!.minute.toString().padLeft(2, '0')}'
      : 'Not checked out';
}

@JsonSerializable()
class PaginatedConstructionSites {
  final List<ConstructionSite> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  const PaginatedConstructionSites({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginatedConstructionSites.fromJson(Map<String, dynamic> json) => _$PaginatedConstructionSitesFromJson(json);
  Map<String, dynamic> toJson() => _$PaginatedConstructionSitesToJson(this);

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}

@JsonSerializable()
class PaginatedConstructionSiteAttendance {
  final List<ConstructionSiteAttendance> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  const PaginatedConstructionSiteAttendance({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginatedConstructionSiteAttendance.fromJson(Map<String, dynamic> json) => _$PaginatedConstructionSiteAttendanceFromJson(json);
  Map<String, dynamic> toJson() => _$PaginatedConstructionSiteAttendanceToJson(this);

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}

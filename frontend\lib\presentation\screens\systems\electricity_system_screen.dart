import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'unified_system_management_screen.dart';

class ElectricitySystemScreen extends ConsumerWidget {
  final String propertyId;
  
  const ElectricitySystemScreen({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return UnifiedSystemManagementScreen(
      propertyId: propertyId,
      systemType: 'ELECTRICITY',
      systemName: 'Electricity Management',
      systemIcon: Icons.electrical_services,
      systemColor: Colors.orange,
    );
  }
}

import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  getEmployeeStatuses,
  createEmployeeStatus,
  updateEmployeeStatus,
  deleteEmployeeStatus
} from '../controllers/employeeConfigController';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Department routes
router.get('/departments', getDepartments);
router.get('/departments/:id', getDepartmentById);
router.post('/departments', createDepartment);
router.put('/departments/:id', updateDepartment);
router.delete('/departments/:id', deleteDepartment);

// Employee status routes
router.get('/statuses', getEmployeeStatuses);
router.post('/statuses', createEmployeeStatus);
router.put('/statuses/:id', updateEmployeeStatus);
router.delete('/statuses/:id', deleteEmployeeStatus);

export default router;

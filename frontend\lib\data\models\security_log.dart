import 'package:json_annotation/json_annotation.dart';

part 'security_log.g.dart';

@JsonSerializable()
class SecurityLog {
  final String id;
  final String propertyId;
  final String activity;
  final String location;
  final String guardName;
  final String status;
  final String? notes;
  final DateTime timestamp;
  final String createdAt;
  final String updatedAt;

  const SecurityLog({
    required this.id,
    required this.propertyId,
    required this.activity,
    required this.location,
    required this.guardName,
    required this.status,
    this.notes,
    required this.timestamp,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SecurityLog.fromJson(Map<String, dynamic> json) => _$SecurityLogFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityLogToJson(this);

  SecurityLog copyWith({
    String? id,
    String? propertyId,
    String? activity,
    String? location,
    String? guardName,
    String? status,
    String? notes,
    DateTime? timestamp,
    String? createdAt,
    String? updatedAt,
  }) {
    return SecurityLog(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      activity: activity ?? this.activity,
      location: location ?? this.location,
      guardName: guardName ?? this.guardName,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      timestamp: timestamp ?? this.timestamp,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isNormal => status.toUpperCase() == 'NORMAL';
  bool get isLogged => status.toUpperCase() == 'LOGGED';
  bool get isCompleted => status.toUpperCase() == 'COMPLETED';
  bool get isVerified => status.toUpperCase() == 'VERIFIED';
  bool get isAlert => status.toUpperCase() == 'ALERT';

  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);
}

@JsonSerializable()
class PaginatedSecurityLogs {
  final List<SecurityLog> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  const PaginatedSecurityLogs({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginatedSecurityLogs.fromJson(Map<String, dynamic> json) => _$PaginatedSecurityLogsFromJson(json);
  Map<String, dynamic> toJson() => _$PaginatedSecurityLogsToJson(this);

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}

@JsonSerializable()
class SecurityIncident {
  final String id;
  final String propertyId;
  final String title;
  final String description;
  final String severity; // LOW, MEDIUM, HIGH, CRITICAL
  final String status; // OPEN, INVESTIGATING, RESOLVED, CLOSED
  final String reportedBy;
  final String? assignedTo;
  final String? location;
  final DateTime incidentDate;
  final DateTime? resolvedDate;
  final List<String> attachments;
  final String createdAt;
  final String updatedAt;

  const SecurityIncident({
    required this.id,
    required this.propertyId,
    required this.title,
    required this.description,
    required this.severity,
    required this.status,
    required this.reportedBy,
    this.assignedTo,
    this.location,
    required this.incidentDate,
    this.resolvedDate,
    required this.attachments,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SecurityIncident.fromJson(Map<String, dynamic> json) => _$SecurityIncidentFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityIncidentToJson(this);

  SecurityIncident copyWith({
    String? id,
    String? propertyId,
    String? title,
    String? description,
    String? severity,
    String? status,
    String? reportedBy,
    String? assignedTo,
    String? location,
    DateTime? incidentDate,
    DateTime? resolvedDate,
    List<String>? attachments,
    String? createdAt,
    String? updatedAt,
  }) {
    return SecurityIncident(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      title: title ?? this.title,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      status: status ?? this.status,
      reportedBy: reportedBy ?? this.reportedBy,
      assignedTo: assignedTo ?? this.assignedTo,
      location: location ?? this.location,
      incidentDate: incidentDate ?? this.incidentDate,
      resolvedDate: resolvedDate ?? this.resolvedDate,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isOpen => status.toUpperCase() == 'OPEN';
  bool get isInvestigating => status.toUpperCase() == 'INVESTIGATING';
  bool get isResolved => status.toUpperCase() == 'RESOLVED';
  bool get isClosed => status.toUpperCase() == 'CLOSED';

  bool get isLowSeverity => severity.toUpperCase() == 'LOW';
  bool get isMediumSeverity => severity.toUpperCase() == 'MEDIUM';
  bool get isHighSeverity => severity.toUpperCase() == 'HIGH';
  bool get isCriticalSeverity => severity.toUpperCase() == 'CRITICAL';

  bool get hasAttachments => attachments.isNotEmpty;
  bool get isAssigned => assignedTo != null;
  bool get hasBeenResolved => resolvedDate != null;

  DateTime get createdDateTime => DateTime.parse(createdAt);
  DateTime get updatedDateTime => DateTime.parse(updatedAt);

  Duration? get resolutionTime => resolvedDate?.difference(incidentDate);
  int? get resolutionDays => resolutionTime?.inDays;
}

@JsonSerializable()
class PaginatedSecurityIncidents {
  final List<SecurityIncident> data;
  final int total;
  final int page;
  final int limit;
  final int totalPages;

  const PaginatedSecurityIncidents({
    required this.data,
    required this.total,
    required this.page,
    required this.limit,
    required this.totalPages,
  });

  factory PaginatedSecurityIncidents.fromJson(Map<String, dynamic> json) => _$PaginatedSecurityIncidentsFromJson(json);
  Map<String, dynamic> toJson() => _$PaginatedSecurityIncidentsToJson(this);

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;
  int get nextPage => hasNextPage ? page + 1 : page;
  int get previousPage => hasPreviousPage ? page - 1 : page;
}

@JsonSerializable()
class SecurityAnalytics {
  final SecurityOverview overview;
  final List<SecurityTrend> trends;
  final Map<String, int> incidentsBySeverity;
  final Map<String, int> incidentsByLocation;
  final TimeRange timeRange;

  const SecurityAnalytics({
    required this.overview,
    required this.trends,
    required this.incidentsBySeverity,
    required this.incidentsByLocation,
    required this.timeRange,
  });

  factory SecurityAnalytics.fromJson(Map<String, dynamic> json) => _$SecurityAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityAnalyticsToJson(this);
}

@JsonSerializable()
class SecurityOverview {
  final int totalIncidents;
  final int openIncidents;
  final int resolvedIncidents;
  final int criticalIncidents;
  final double avgResolutionTime;
  final double resolutionRate;

  const SecurityOverview({
    required this.totalIncidents,
    required this.openIncidents,
    required this.resolvedIncidents,
    required this.criticalIncidents,
    required this.avgResolutionTime,
    required this.resolutionRate,
  });

  factory SecurityOverview.fromJson(Map<String, dynamic> json) => _$SecurityOverviewFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityOverviewToJson(this);
}

@JsonSerializable()
class SecurityTrend {
  final String date;
  final int incidents;
  final int resolved;
  final int critical;

  const SecurityTrend({
    required this.date,
    required this.incidents,
    required this.resolved,
    required this.critical,
  });

  factory SecurityTrend.fromJson(Map<String, dynamic> json) => _$SecurityTrendFromJson(json);
  Map<String, dynamic> toJson() => _$SecurityTrendToJson(this);

  DateTime get dateTime => DateTime.parse(date);
}

@JsonSerializable()
class TimeRange {
  final String period;
  final DateTime startDate;
  final DateTime endDate;

  const TimeRange({
    required this.period,
    required this.startDate,
    required this.endDate,
  });

  factory TimeRange.fromJson(Map<String, dynamic> json) => _$TimeRangeFromJson(json);
  Map<String, dynamic> toJson() => _$TimeRangeToJson(this);
}

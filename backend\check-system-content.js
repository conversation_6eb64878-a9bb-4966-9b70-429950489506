const { PrismaClient } = require('@prisma/client');

async function checkSystemContent() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== CHECKING PROPERTIES ===');
    const properties = await prisma.property.findMany({
      select: {
        id: true,
        name: true,
        type: true,
      }
    });
    
    console.log('Properties found:', properties.length);
    properties.forEach(prop => {
      console.log(`- ${prop.name} (${prop.type}) - ID: ${prop.id}`);
    });
    
    console.log('\n=== CHECKING WATER SYSTEM CONTENT ===');
    const waterContent = await prisma.systemContent.findMany({
      where: {
        systemType: 'WATER',
        contentType: 'tab_content'
      },
      select: {
        id: true,
        propertyId: true,
        title: true,
        richContent: true,
      }
    });
    
    console.log('Water tab content found:', waterContent.length);
    
    // Group by property
    const contentByProperty = {};
    waterContent.forEach(content => {
      if (!contentByProperty[content.propertyId]) {
        contentByProperty[content.propertyId] = [];
      }
      contentByProperty[content.propertyId].push({
        title: content.title,
        contentLength: content.richContent?.length || 0
      });
    });
    
    console.log('\nContent by property:');
    Object.keys(contentByProperty).forEach(propertyId => {
      const property = properties.find(p => p.id === propertyId);
      console.log(`\n${property?.name || 'Unknown'} (${propertyId}):`);
      contentByProperty[propertyId].forEach(content => {
        console.log(`  - ${content.title}: ${content.contentLength} chars`);
      });
    });
    
    // Check for properties without content
    const propertiesWithoutContent = properties.filter(prop => 
      !contentByProperty[prop.id]
    );
    
    if (propertiesWithoutContent.length > 0) {
      console.log('\n=== PROPERTIES WITHOUT WATER CONTENT ===');
      propertiesWithoutContent.forEach(prop => {
        console.log(`- ${prop.name} (${prop.id})`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSystemContent();

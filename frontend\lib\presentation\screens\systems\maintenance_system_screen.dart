import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'unified_system_management_screen.dart';

class MaintenanceSystemScreen extends ConsumerWidget {
  final String propertyId;
  
  const MaintenanceSystemScreen({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return UnifiedSystemManagementScreen(
      propertyId: propertyId,
      systemType: 'MAINTENANCE',
      systemName: 'Maintenance Management',
      systemIcon: Icons.build,
      systemColor: Colors.green,
    );
  }
}

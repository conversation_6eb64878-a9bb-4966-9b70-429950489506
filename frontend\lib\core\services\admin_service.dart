import '../constants/api_constants.dart';
import '../network/api_client.dart';
import '../utils/logger.dart';
import '../utils/api_response.dart';
import '../../data/models/user_model.dart';

class AdminService {
  final ApiClient _apiClient;

  AdminService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();

  // Role Management
  Future<ApiResponse<List<Map<String, dynamic>>>> getRoles() async {
    try {
      final response = await _apiClient.get(ApiConstants.roles);
      if (response.success) {
        return ApiResponse.success(List<Map<String, dynamic>>.from(response.data['data'] ?? []));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error fetching roles: $e');
      return ApiResponse.error('Failed to fetch roles');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getRoleById(String roleId) async {
    try {
      final response = await _apiClient.get(
        ApiConstants.role.replaceAll('{id}', roleId),
      );
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error fetching role: $e');
      return ApiResponse.error('Failed to fetch role');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> createRole({
    required String name,
    String? description,
    List<String>? permissions,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.roles,
        data: {
          'name': name,
          'description': description,
          'permissions': permissions,
        },
      );
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error creating role: $e');
      return ApiResponse.error('Failed to create role');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> updateRole({
    required String roleId,
    String? name,
    String? description,
    List<String>? permissions,
  }) async {
    try {
      final response = await _apiClient.put(
        ApiConstants.role.replaceAll('{id}', roleId),
        data: {
          if (name != null) 'name': name,
          if (description != null) 'description': description,
          if (permissions != null) 'permissions': permissions,
        },
      );
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error updating role: $e');
      return ApiResponse.error('Failed to update role');
    }
  }

  Future<ApiResponse<void>> deleteRole(String roleId) async {
    try {
      final response = await _apiClient.delete(
        ApiConstants.role.replaceAll('{id}', roleId),
      );
      if (response.success) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error deleting role: $e');
      return ApiResponse.error('Failed to delete role');
    }
  }

  // User Management
  Future<ApiResponse<Map<String, dynamic>>> createUser({
    required String name,
    required String email,
    required String phone,
    required UserRole role,
    required String password,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.users,
        data: {
          'name': name,
          'email': email,
          'phone': phone,
          'role': role.toString().split('.').last.toUpperCase(),
          'password': password,
        },
      );
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error creating user: $e');
      return ApiResponse.error('Failed to create user');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> updateUser({
    required String userId,
    String? name,
    String? email,
    String? phone,
    UserRole? role,
    bool? isActive,
  }) async {
    try {
      final response = await _apiClient.put(
        ApiConstants.user.replaceAll('{id}', userId),
        data: {
          if (name != null) 'name': name,
          if (email != null) 'email': email,
          if (phone != null) 'phone': phone,
          if (role != null) 'role': role.toString().split('.').last.toUpperCase(),
          if (isActive != null) 'isActive': isActive,
        },
      );
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error updating user: $e');
      return ApiResponse.error('Failed to update user');
    }
  }

  Future<ApiResponse<void>> deleteUser(String userId) async {
    try {
      final response = await _apiClient.delete(
        ApiConstants.user.replaceAll('{id}', userId),
      );
      if (response.success) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error deleting user: $e');
      return ApiResponse.error('Failed to delete user');
    }
  }

  // Permission Management
  Future<ApiResponse<Map<String, dynamic>>> getPermissions() async {
    try {
      final response = await _apiClient.get(ApiConstants.permissions);
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error fetching permissions: $e');
      return ApiResponse.error('Failed to fetch permissions');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getUserPermissions(String userId) async {
    try {
      final response = await _apiClient.get('${ApiConstants.users}/$userId/permissions');
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error fetching user permissions: $e');
      return ApiResponse.error('Failed to fetch user permissions');
    }
  }

  Future<ApiResponse<void>> updateUserPermissions(
    String userId,
    Map<String, List<String>> permissions,
  ) async {
    try {
      final response = await _apiClient.put(
        '${ApiConstants.users}/$userId/permissions',
        data: {'permissions': permissions},
      );
      if (response.success) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error updating user permissions: $e');
      return ApiResponse.error('Failed to update user permissions');
    }
  }

  // User Role Assignment
  Future<ApiResponse<Map<String, dynamic>>> assignUserRole({
    required String userId,
    required String roleId,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.userRoles,
        data: {
          'userId': userId,
          'roleId': roleId,
        },
      );
      if (response.success) {
        return ApiResponse.success(Map<String, dynamic>.from(response.data['data'] ?? {}));
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error assigning user role: $e');
      return ApiResponse.error('Failed to assign user role');
    }
  }

  Future<ApiResponse<void>> removeUserRole({
    required String userId,
    required String roleId,
  }) async {
    try {
      final response = await _apiClient.delete(
        '${ApiConstants.userRoles}/$userId/$roleId',
      );
      if (response.success) {
        return ApiResponse.success(null);
      } else {
        return ApiResponse.error(response.error ?? 'Request failed');
      }
    } catch (e) {
      Logger.error('Error removing user role: $e');
      return ApiResponse.error('Failed to remove user role');
    }
  }
}



// Role model
class Role {
  final String id;
  final String name;
  final String? description;
  final List<Permission> permissions;
  final List<User> users;
  final int userCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  Role({
    required this.id,
    required this.name,
    this.description,
    required this.permissions,
    required this.users,
    required this.userCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((p) => Permission.fromJson(p))
          .toList() ?? [],
      users: (json['users'] as List<dynamic>?)
          ?.map((u) => User.fromJson(u))
          .toList() ?? [],
      userCount: json['userCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissions': permissions.map((p) => p.toJson()).toList(),
      'users': users.map((u) => u.toJson()).toList(),
      'userCount': userCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

// Permission model
class Permission {
  final String id;
  final String name;
  final String? description;
  final String? category;
  final List<Role> roles;
  final int roleCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  Permission({
    required this.id,
    required this.name,
    this.description,
    this.category,
    required this.roles,
    required this.roleCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    return Permission(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      category: json['category'],
      roles: (json['roles'] as List<dynamic>?)
          ?.map((r) => Role.fromJson(r))
          .toList() ?? [],
      roleCount: json['roleCount'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'roles': roles.map((r) => r.toJson()).toList(),
      'roleCount': roleCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

// User model (simplified for admin context)
class User {
  final String id;
  final String name;
  final String email;

  User({
    required this.id,
    required this.name,
    required this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
    };
  }
}

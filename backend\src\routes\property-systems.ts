import { Router } from 'express';
import { PrismaClient, SystemType, UserRole } from '@prisma/client';
import { authenticateToken, authorize } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const systemConfigSchema = z.object({
  systemType: z.nativeEnum(SystemType),
  isEnabled: z.boolean(),
  displayName: z.string().optional(),
  displayOrder: z.number().optional(),
  configuration: z.record(z.any()).optional(),
});

const systemContentSchema = z.object({
  systemType: z.nativeEnum(SystemType),
  contentType: z.string(),
  title: z.string(),
  content: z.record(z.any()),
  richContent: z.string().optional(), // Large text content for rich text editor
  contentFormat: z.enum(['markdown', 'html', 'json']).optional(), // Format of richContent
  displayOrder: z.number().optional(),
  isActive: z.boolean().default(true),
  isTab: z.boolean().default(false), // Indicates if this content represents a tab
  tabIcon: z.string().optional(), // Icon for tab
  metadata: z.record(z.any()).optional(),
});

const systemContactSchema = z.object({
  systemType: z.nativeEnum(SystemType),
  name: z.string(),
  organization: z.string().optional(),
  phone: z.string(),
  email: z.string().email().optional(),
  contactType: z.string(),
  isActive: z.boolean().default(true),
  displayOrder: z.number().optional(),
});

// Get property system configurations
router.get('/:propertyId/configs', authenticateToken, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const user = req.user!;

    // Check property access
    if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
      return res.status(403).json({ error: 'Access denied to this property' });
    }

    const configs = await prisma.propertySystemConfig.findMany({
      where: { propertyId },
      orderBy: [
        { displayOrder: 'asc' },
        { systemType: 'asc' }
      ],
    });

    // If no configs exist, create default ones
    if (configs.length === 0) {
      const defaultSystems = [
        { systemType: SystemType.WATER, displayName: 'Water Management', displayOrder: 1 },
        { systemType: SystemType.ELECTRICITY, displayName: 'Electricity Management', displayOrder: 2 },
        { systemType: SystemType.SECURITY, displayName: 'Security Management', displayOrder: 3 },
        { systemType: SystemType.INTERNET, displayName: 'Internet Management', displayOrder: 4 },
        { systemType: SystemType.MAINTENANCE, displayName: 'Maintenance Management', displayOrder: 5 },
      ];

      const createdConfigs = await Promise.all(
        defaultSystems.map(system =>
          prisma.propertySystemConfig.create({
            data: {
              propertyId,
              ...system,
              isEnabled: true,
              configuration: {},
            },
          })
        )
      );

      return res.json({
        success: true,
        data: createdConfigs,
      });
    }

    res.json({
      success: true,
      data: configs,
    });
  } catch (error) {
    console.error('Error fetching property system configs:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update property system configuration
router.put('/:propertyId/configs/:configId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId, configId } = req.params;
      const user = req.user!;
      const updateData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      const updatedConfig = await prisma.propertySystemConfig.update({
        where: {
          id: configId,
          propertyId,
        },
        data: updateData,
      });

      res.json({
        success: true,
        data: updatedConfig,
      });
    } catch (error) {
      console.error('Error updating property system config:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Create system content
router.post('/:propertyId/content',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.SECURITY_PERSONNEL, UserRole.MAINTENANCE_STAFF]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId } = req.params;
      const user = req.user!;
      const contentData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      // Role-based system type restrictions
      if (user.role === UserRole.SECURITY_PERSONNEL && contentData.systemType !== 'SECURITY') {
        return res.status(403).json({ error: 'Security personnel can only manage security system content' });
      }

      if (user.role === UserRole.MAINTENANCE_STAFF && !['MAINTENANCE', 'WATER', 'ELECTRICITY'].includes(contentData.systemType)) {
        return res.status(403).json({ error: 'Maintenance staff can only manage maintenance, water, and electricity system content' });
      }

      const content = await prisma.systemContent.create({
        data: {
          propertyId,
          ...contentData,
        },
      });

      res.status(201).json({
        success: true,
        data: content,
      });
    } catch (error) {
      console.error('Error creating system content:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get system content
router.get('/:propertyId/content', authenticateToken, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const { systemType, contentType } = req.query;
    const user = req.user!;

    // Check property access
    if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
      return res.status(403).json({ error: 'Access denied to this property' });
    }

    const whereClause: any = {
      propertyId,
      isActive: true,
    };

    if (systemType) {
      whereClause.systemType = systemType;
    }

    if (contentType) {
      whereClause.contentType = contentType;
    }

    const content = await prisma.systemContent.findMany({
      where: whereClause,
      orderBy: [
        { displayOrder: 'asc' },
        { createdAt: 'desc' }
      ],
    });

    res.json({
      success: true,
      data: content,
    });
  } catch (error) {
    console.error('Error fetching system content:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update system content
router.put('/:propertyId/content/:contentId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.SECURITY_PERSONNEL, UserRole.MAINTENANCE_STAFF]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId, contentId } = req.params;
      const user = req.user!;
      const updateData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      // Get existing content to check system type
      const existingContent = await prisma.systemContent.findUnique({
        where: { id: contentId, propertyId },
      });

      if (!existingContent) {
        return res.status(404).json({ error: 'Content not found' });
      }

      // Role-based system type restrictions
      if (user.role === UserRole.SECURITY_PERSONNEL && existingContent.systemType !== 'SECURITY') {
        return res.status(403).json({ error: 'Security personnel can only manage security system content' });
      }

      if (user.role === UserRole.MAINTENANCE_STAFF && !['MAINTENANCE', 'WATER', 'ELECTRICITY'].includes(existingContent.systemType)) {
        return res.status(403).json({ error: 'Maintenance staff can only manage maintenance, water, and electricity system content' });
      }

      const updatedContent = await prisma.systemContent.update({
        where: {
          id: contentId,
          propertyId,
        },
        data: updateData,
      });

      res.json({
        success: true,
        data: updatedContent,
      });
    } catch (error) {
      console.error('Error updating system content:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete system content
router.delete('/:propertyId/content/:contentId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER, UserRole.SECURITY_PERSONNEL, UserRole.MAINTENANCE_STAFF]),
  async (req, res) => {
    try {
      const { propertyId, contentId } = req.params;
      const user = req.user!;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      // Get existing content to check system type
      const existingContent = await prisma.systemContent.findUnique({
        where: { id: contentId, propertyId },
      });

      if (!existingContent) {
        return res.status(404).json({ error: 'Content not found' });
      }

      // Role-based system type restrictions
      if (user.role === UserRole.SECURITY_PERSONNEL && existingContent.systemType !== 'SECURITY') {
        return res.status(403).json({ error: 'Security personnel can only manage security system content' });
      }

      if (user.role === UserRole.MAINTENANCE_STAFF && !['MAINTENANCE', 'WATER', 'ELECTRICITY'].includes(existingContent.systemType)) {
        return res.status(403).json({ error: 'Maintenance staff can only manage maintenance, water, and electricity system content' });
      }

      await prisma.systemContent.delete({
        where: {
          id: contentId,
          propertyId,
        },
      });

      res.json({
        success: true,
        message: 'Content deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting system content:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Create system contact
router.post('/:propertyId/contacts',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId } = req.params;
      const user = req.user!;
      const contactData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      const contact = await prisma.systemContact.create({
        data: {
          propertyId,
          ...contactData,
        },
      });

      res.status(201).json({
        success: true,
        data: contact,
      });
    } catch (error) {
      console.error('Error creating system contact:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get system contacts
router.get('/:propertyId/contacts', authenticateToken, async (req, res) => {
  try {
    const { propertyId } = req.params;
    const { systemType } = req.query;
    const user = req.user!;

    // Check property access
    if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
      return res.status(403).json({ error: 'Access denied to this property' });
    }

    const whereClause: any = {
      propertyId,
      isActive: true,
    };

    if (systemType) {
      whereClause.systemType = systemType;
    }

    const contacts = await prisma.systemContact.findMany({
      where: whereClause,
      orderBy: [
        { displayOrder: 'asc' },
        { name: 'asc' }
      ],
    });

    res.json({
      success: true,
      data: contacts,
    });
  } catch (error) {
    console.error('Error fetching system contacts:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update system contact
router.put('/:propertyId/contacts/:contactId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId, contactId } = req.params;
      const user = req.user!;
      const updateData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      const updatedContact = await prisma.systemContact.update({
        where: {
          id: contactId,
          propertyId,
        },
        data: updateData,
      });

      res.json({
        success: true,
        data: updatedContact,
      });
    } catch (error) {
      console.error('Error updating system contact:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete system contact
router.delete('/:propertyId/contacts/:contactId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  async (req, res) => {
    try {
      const { propertyId, contactId } = req.params;
      const user = req.user!;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      await prisma.systemContact.delete({
        where: {
          id: contactId,
          propertyId,
        },
      });

      res.json({
        success: true,
        message: 'Contact deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting system contact:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get system tabs
router.get('/:propertyId/tabs/:systemType', authenticateToken, async (req, res) => {
  try {
    const { propertyId, systemType } = req.params;
    const user = req.user!;

    // Check property access
    if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
      return res.status(403).json({ error: 'Access denied to this property' });
    }

    const tabs = await prisma.systemContent.findMany({
      where: {
        propertyId,
        systemType: systemType as SystemType,
        isTab: true,
        isActive: true,
      },
      orderBy: [
        { displayOrder: 'asc' },
        { createdAt: 'asc' }
      ],
    });

    res.json({
      success: true,
      data: tabs,
    });
  } catch (error) {
    console.error('Error fetching system tabs:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create system tab
router.post('/:propertyId/tabs',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId } = req.params;
      const user = req.user!;
      const tabData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      const tab = await prisma.systemContent.create({
        data: {
          propertyId,
          ...tabData,
          isTab: true,
        },
      });

      res.status(201).json({
        success: true,
        data: tab,
      });
    } catch (error) {
      console.error('Error creating system tab:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Update system tab
router.put('/:propertyId/tabs/:tabId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  validateRequest([]),
  async (req, res) => {
    try {
      const { propertyId, tabId } = req.params;
      const user = req.user!;
      const updateData = req.body;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      const updatedTab = await prisma.systemContent.update({
        where: {
          id: tabId,
          propertyId,
          isTab: true,
        },
        data: updateData,
      });

      res.json({
        success: true,
        data: updatedTab,
      });
    } catch (error) {
      console.error('Error updating system tab:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete system tab
router.delete('/:propertyId/tabs/:tabId',
  authenticateToken,
  authorize([UserRole.SUPER_ADMIN, UserRole.PROPERTY_MANAGER]),
  async (req, res) => {
    try {
      const { propertyId, tabId } = req.params;
      const user = req.user!;

      // Check property access
      if (user.role !== UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
        return res.status(403).json({ error: 'Access denied to this property' });
      }

      await prisma.systemContent.delete({
        where: {
          id: tabId,
          propertyId,
          isTab: true,
        },
      });

      res.json({
        success: true,
        message: 'Tab deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting system tab:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

export default router;

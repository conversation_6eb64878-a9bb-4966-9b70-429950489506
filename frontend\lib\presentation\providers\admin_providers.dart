import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/admin_service.dart';
import '../../core/services/service_locator.dart';

// Service provider
final adminServiceProvider = Provider<AdminService>((ref) {
  return getService<AdminService>();
});

// Roles providers
final rolesProvider = FutureProvider<List<Role>>((ref) async {
  final adminService = ref.read(adminServiceProvider);
  final response = await adminService.getRoles();
  
  if (response.success && response.data != null) {
    return response.data!.map((roleJson) => Role.fromJson(roleJson)).toList();
  } else {
    throw Exception(response.message ?? 'Failed to fetch roles');
  }
});

final roleByIdProvider = FutureProvider.family<Role, String>((ref, roleId) async {
  final adminService = ref.read(adminServiceProvider);
  final response = await adminService.getRoleById(roleId);
  
  if (response.success && response.data != null) {
    return Role.fromJson(response.data!);
  } else {
    throw Exception(response.message ?? 'Failed to fetch role');
  }
});

// Permissions provider
final permissionsProvider = FutureProvider<List<Permission>>((ref) async {
  final adminService = ref.read(adminServiceProvider);
  final response = await adminService.getPermissions();
  
  if (response.success && response.data != null) {
    return response.data!.entries.map((entry) => Permission.fromJson({
      'id': entry.key,
      'name': entry.key,
      'description': entry.value.toString(),
    })).toList();
  } else {
    throw Exception(response.message ?? 'Failed to fetch permissions');
  }
});

// Role management state providers
final roleFormProvider = StateNotifierProvider<RoleFormNotifier, RoleFormState>((ref) {
  return RoleFormNotifier(ref.read(adminServiceProvider));
});

class RoleFormState {
  final bool isLoading;
  final String? error;
  final String? successMessage;
  final Role? role;

  RoleFormState({
    this.isLoading = false,
    this.error,
    this.successMessage,
    this.role,
  });

  RoleFormState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
    Role? role,
  }) {
    return RoleFormState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
      role: role ?? this.role,
    );
  }
}

class RoleFormNotifier extends StateNotifier<RoleFormState> {
  final AdminService _adminService;

  RoleFormNotifier(this._adminService) : super(RoleFormState());

  Future<void> createRole({
    required String name,
    String? description,
    List<String>? permissions,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _adminService.createRole(
        name: name,
        description: description,
        permissions: permissions,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Role created successfully',
          role: Role.fromJson(response.data!),
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create role',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create role: $e',
      );
    }
  }

  Future<void> updateRole({
    required String roleId,
    String? name,
    String? description,
    List<String>? permissions,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _adminService.updateRole(
        roleId: roleId,
        name: name,
        description: description,
        permissions: permissions,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Role updated successfully',
          role: Role.fromJson(response.data!),
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to update role',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update role: $e',
      );
    }
  }

  Future<void> deleteRole(String roleId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _adminService.deleteRole(roleId);

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Role deleted successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to delete role',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete role: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// User role assignment providers
final userRoleAssignmentProvider = StateNotifierProvider<UserRoleAssignmentNotifier, UserRoleAssignmentState>((ref) {
  return UserRoleAssignmentNotifier(ref.read(adminServiceProvider));
});

class UserRoleAssignmentState {
  final bool isLoading;
  final String? error;
  final String? successMessage;

  UserRoleAssignmentState({
    this.isLoading = false,
    this.error,
    this.successMessage,
  });

  UserRoleAssignmentState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
  }) {
    return UserRoleAssignmentState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
    );
  }
}

class UserRoleAssignmentNotifier extends StateNotifier<UserRoleAssignmentState> {
  final AdminService _adminService;

  UserRoleAssignmentNotifier(this._adminService) : super(UserRoleAssignmentState());

  Future<void> assignUserRole({
    required String userId,
    required String roleId,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _adminService.assignUserRole(
        userId: userId,
        roleId: roleId,
      );

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Role assigned successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to assign role',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to assign role: $e',
      );
    }
  }

  Future<void> removeUserRole({
    required String userId,
    required String roleId,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _adminService.removeUserRole(
        userId: userId,
        roleId: roleId,
      );

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Role removed successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to remove role',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to remove role: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/system.dart';
import '../../data/repositories/property_system_repository.dart';
import '../../core/utils/logger.dart';
import '../../core/services/service_locator.dart';

// Repository provider
final propertySystemRepositoryProvider = Provider<PropertySystemRepository>((ref) {
  final apiClient = serviceLocator.apiClient;
  return PropertySystemRepository(apiClient);
});

// Property System Configs Provider
final propertySystemConfigsProvider = FutureProvider.family<List<PropertySystemConfig>, String>((ref, propertyId) async {
  final repository = ref.read(propertySystemRepositoryProvider);
  final cacheManager = serviceLocator.cacheManager;
  final connectivityManager = serviceLocator.connectivityManager;
  
  final cacheKey = 'property_system_configs_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getPropertySystemConfigs(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!.map((config) => config.toJson()).toList(),
          duration: const Duration(hours: 1),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData.map((item) => PropertySystemConfig.fromJson(item)).toList();
    }

    return [];
  } catch (e) {
    Logger.error('Error in propertySystemConfigsProvider: $e');
    return [];
  }
});

// System Content Provider
final systemContentProvider = FutureProvider.family<List<SystemContent>, SystemContentParams>((ref, params) async {
  final repository = ref.read(propertySystemRepositoryProvider);
  final cacheManager = serviceLocator.cacheManager;
  final connectivityManager = serviceLocator.connectivityManager;
  
  final cacheKey = 'system_content_${params.propertyId}_${params.systemType}_${params.contentType}';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getSystemContent(
        propertyId: params.propertyId,
        systemType: params.systemType,
        contentType: params.contentType,
      );

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!.map((content) => content.toJson()).toList(),
          duration: const Duration(minutes: 30),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData.map((item) => SystemContent.fromJson(item)).toList();
    }

    return [];
  } catch (e) {
    Logger.error('Error in systemContentProvider: $e');
    return [];
  }
});

// System Contacts Provider
final systemContactsProvider = FutureProvider.family<List<SystemContact>, SystemContactParams>((ref, params) async {
  final repository = ref.read(propertySystemRepositoryProvider);
  final cacheManager = serviceLocator.cacheManager;
  final connectivityManager = serviceLocator.connectivityManager;
  
  final cacheKey = 'system_contacts_${params.propertyId}_${params.systemType}';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getSystemContacts(
        propertyId: params.propertyId,
        systemType: params.systemType,
      );

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!.map((contact) => contact.toJson()).toList(),
          duration: const Duration(hours: 2),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData.map((item) => SystemContact.fromJson(item)).toList();
    }

    return [];
  } catch (e) {
    Logger.error('Error in systemContactsProvider: $e');
    return [];
  }
});

// Property System Management Provider (for admin operations)
final propertySystemManagementProvider = StateNotifierProvider<PropertySystemManagementNotifier, PropertySystemManagementState>((ref) {
  final repository = ref.read(propertySystemRepositoryProvider);
  return PropertySystemManagementNotifier(repository);
});

// State classes
class SystemContentParams {
  final String propertyId;
  final String? systemType;
  final String? contentType;

  const SystemContentParams({
    required this.propertyId,
    this.systemType,
    this.contentType,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SystemContentParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          systemType == other.systemType &&
          contentType == other.contentType;

  @override
  int get hashCode => propertyId.hashCode ^ systemType.hashCode ^ contentType.hashCode;
}

class SystemContactParams {
  final String propertyId;
  final String? systemType;

  const SystemContactParams({
    required this.propertyId,
    this.systemType,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SystemContactParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          systemType == other.systemType;

  @override
  int get hashCode => propertyId.hashCode ^ systemType.hashCode;
}

class PropertySystemManagementState {
  final bool isLoading;
  final String? error;
  final String? successMessage;

  const PropertySystemManagementState({
    this.isLoading = false,
    this.error,
    this.successMessage,
  });

  PropertySystemManagementState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
  }) {
    return PropertySystemManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
    );
  }
}

class PropertySystemManagementNotifier extends StateNotifier<PropertySystemManagementState> {
  final PropertySystemRepository _repository;

  PropertySystemManagementNotifier(this._repository) : super(const PropertySystemManagementState());

  Future<bool> updateSystemConfig({
    required String propertyId,
    required String configId,
    required Map<String, dynamic> data,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _repository.updatePropertySystemConfig(
        propertyId: propertyId,
        configId: configId,
        data: data,
      );

      if (response.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          successMessage: 'System configuration updated successfully',
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'Failed to update system configuration',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error updating system configuration: $e',
      );
      return false;
    }
  }

  Future<bool> createSystemContent({
    required String propertyId,
    required Map<String, dynamic> data,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _repository.createSystemContent(
        propertyId: propertyId,
        data: data,
      );

      if (response.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          successMessage: 'System content created successfully',
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'Failed to create system content',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error creating system content: $e',
      );
      return false;
    }
  }

  Future<bool> updateSystemContent({
    required String propertyId,
    required String contentId,
    required Map<String, dynamic> data,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _repository.updateSystemContent(
        propertyId: propertyId,
        contentId: contentId,
        data: data,
      );

      if (response.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          successMessage: 'System content updated successfully',
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error ?? 'Failed to update system content',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error updating system content: $e',
      );
      return false;
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

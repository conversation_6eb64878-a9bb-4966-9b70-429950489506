
import '../constants/api_constants.dart';
import '../network/api_client.dart';
import '../utils/logger.dart';
import '../utils/api_response.dart';

class ConstructionSitesService {
  final ApiClient _apiClient;

  ConstructionSitesService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();

  // Get all construction sites
  Future<ApiResponse<List<ConstructionSite>>> getConstructionSites() async {
    try {
      final response = await _apiClient.get(ApiConstants.constructionSites);

      if (response.isSuccess && response.data != null) {
        final sites = (response.data['data'] as List<dynamic>)
            .map((site) => ConstructionSite.fromJson(site))
            .toList();

        return ApiResponse<List<ConstructionSite>>(
          success: true,
          data: sites,
          message: response.message,
        );
      } else {
        return ApiResponse.error(response.message ?? 'Failed to fetch construction sites');
      }
    } catch (e) {
      Logger.error('Error fetching construction sites: $e');
      return ApiResponse.error('Failed to fetch construction sites');
    }
  }

  // Get construction site by ID
  Future<ApiResponse<ConstructionSite>> getConstructionSiteById(String siteId) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.constructionSites}/$siteId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<ConstructionSite>(
          success: true,
          data: ConstructionSite.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch construction site');
      }
    } catch (e) {
      Logger.error('Error fetching construction site: $e');
      return ApiResponse.error('Failed to fetch construction site');
    }
  }

  // Create construction site
  Future<ApiResponse<ConstructionSite>> createConstructionSite({
    required String propertyId,
    required String name,
    required String location,
    required int workers,
    required String status,
    required double progress,
    String? description,
    String? startDate,
    String? expectedEndDate,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.constructionSites,
        data: {
          'propertyId': propertyId,
          'name': name,
          'location': location,
          'workers': workers,
          'status': status,
          'progress': progress,
          'description': description,
          'startDate': startDate,
          'expectedEndDate': expectedEndDate,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<ConstructionSite>(
          success: true,
          data: ConstructionSite.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to create construction site');
      }
    } catch (e) {
      Logger.error('Error creating construction site: $e');
      return ApiResponse.error('Failed to create construction site');
    }
  }

  // Update construction site
  Future<ApiResponse<ConstructionSite>> updateConstructionSite({
    required String siteId,
    String? name,
    String? location,
    int? workers,
    String? status,
    double? progress,
    String? description,
    String? startDate,
    String? expectedEndDate,
  }) async {
    try {
      final response = await _apiClient.put(
        '${ApiConstants.constructionSites}/$siteId',
        data: {
          if (name != null) 'name': name,
          if (location != null) 'location': location,
          if (workers != null) 'workers': workers,
          if (status != null) 'status': status,
          if (progress != null) 'progress': progress,
          if (description != null) 'description': description,
          if (startDate != null) 'startDate': startDate,
          if (expectedEndDate != null) 'expectedEndDate': expectedEndDate,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<ConstructionSite>(
          success: true,
          data: ConstructionSite.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update construction site');
      }
    } catch (e) {
      Logger.error('Error updating construction site: $e');
      return ApiResponse.error('Failed to update construction site');
    }
  }

  // Delete construction site
  Future<ApiResponse<void>> deleteConstructionSite(String siteId) async {
    try {
      final response = await _apiClient.delete(
        '${ApiConstants.constructionSites}/$siteId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to delete construction site');
      }
    } catch (e) {
      Logger.error('Error deleting construction site: $e');
      return ApiResponse.error('Failed to delete construction site');
    }
  }

  // Record attendance
  Future<ApiResponse<ConstructionSiteAttendance>> recordAttendance({
    required String siteId,
    required String date,
    required int presentWorkers,
    String? notes,
  }) async {
    try {
      final response = await _apiClient.post(
        '${ApiConstants.constructionSites}/$siteId/attendance',
        data: {
          'date': date,
          'presentWorkers': presentWorkers,
          'notes': notes,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<ConstructionSiteAttendance>(
          success: true,
          data: ConstructionSiteAttendance.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to record attendance');
      }
    } catch (e) {
      Logger.error('Error recording attendance: $e');
      return ApiResponse.error('Failed to record attendance');
    }
  }

  // Get attendance
  Future<ApiResponse<PaginatedAttendance>> getAttendance({
    required String siteId,
    String? startDate,
    String? endDate,
    int page = 1,
    int limit = 30,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final response = await _apiClient.get(
        '${ApiConstants.constructionSites}/$siteId/attendance',
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        return ApiResponse<PaginatedAttendance>(
          success: true,
          data: PaginatedAttendance.fromJson(response.data),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch attendance');
      }
    } catch (e) {
      Logger.error('Error fetching attendance: $e');
      return ApiResponse.error('Failed to fetch attendance');
    }
  }
}

// Construction Site Models
class ConstructionSite {
  final String id;
  final String propertyId;
  final String name;
  final String location;
  final int workers;
  final int presentToday;
  final int attendanceRate;
  final String status;
  final double progress;
  final String? description;
  final DateTime? startDate;
  final DateTime? expectedEndDate;
  final Property? property;
  final int totalAttendanceRecords;
  final DateTime createdAt;
  final DateTime updatedAt;

  ConstructionSite({
    required this.id,
    required this.propertyId,
    required this.name,
    required this.location,
    required this.workers,
    required this.presentToday,
    required this.attendanceRate,
    required this.status,
    required this.progress,
    this.description,
    this.startDate,
    this.expectedEndDate,
    this.property,
    required this.totalAttendanceRecords,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ConstructionSite.fromJson(Map<String, dynamic> json) {
    return ConstructionSite(
      id: json['id'],
      propertyId: json['propertyId'],
      name: json['name'],
      location: json['location'],
      workers: json['workers'] ?? 0,
      presentToday: json['presentToday'] ?? 0,
      attendanceRate: json['attendanceRate'] ?? 0,
      status: json['status'],
      progress: (json['progress'] ?? 0).toDouble(),
      description: json['description'],
      startDate: json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      expectedEndDate: json['expectedEndDate'] != null ? DateTime.parse(json['expectedEndDate']) : null,
      property: json['property'] != null ? Property.fromJson(json['property']) : null,
      totalAttendanceRecords: json['totalAttendanceRecords'] ?? 0,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'propertyId': propertyId,
      'name': name,
      'location': location,
      'workers': workers,
      'presentToday': presentToday,
      'attendanceRate': attendanceRate,
      'status': status,
      'progress': progress,
      'description': description,
      'startDate': startDate?.toIso8601String(),
      'expectedEndDate': expectedEndDate?.toIso8601String(),
      'property': property?.toJson(),
      'totalAttendanceRecords': totalAttendanceRecords,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class ConstructionSiteAttendance {
  final String id;
  final String constructionSiteId;
  final DateTime date;
  final int presentWorkers;
  final String? notes;
  final String recordedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  ConstructionSiteAttendance({
    required this.id,
    required this.constructionSiteId,
    required this.date,
    required this.presentWorkers,
    this.notes,
    required this.recordedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ConstructionSiteAttendance.fromJson(Map<String, dynamic> json) {
    return ConstructionSiteAttendance(
      id: json['id'],
      constructionSiteId: json['constructionSiteId'],
      date: DateTime.parse(json['date']),
      presentWorkers: json['presentWorkers'] ?? 0,
      notes: json['notes'],
      recordedBy: json['recordedBy'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

class Property {
  final String id;
  final String name;

  Property({
    required this.id,
    required this.name,
  });

  factory Property.fromJson(Map<String, dynamic> json) {
    return Property(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class PaginatedAttendance {
  final List<ConstructionSiteAttendance> data;
  final Pagination pagination;

  PaginatedAttendance({
    required this.data,
    required this.pagination,
  });

  factory PaginatedAttendance.fromJson(Map<String, dynamic> json) {
    return PaginatedAttendance(
      data: (json['data'] as List<dynamic>)
          .map((attendance) => ConstructionSiteAttendance.fromJson(attendance))
          .toList(),
      pagination: Pagination.fromJson(json['pagination']),
    );
  }
}

class Pagination {
  final int page;
  final int limit;
  final int total;
  final int totalPages;

  Pagination({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      page: json['page'],
      limit: json['limit'],
      total: json['total'],
      totalPages: json['totalPages'],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/repositories/property_repository.dart' show PropertyDetail, PropertyUser;
import '../../providers/properties_providers.dart';
// Removed enhanced_system_content_management_screen import - content editing should be done in system screens
import '../../../data/models/system.dart';
import '../../providers/property_system_providers.dart';

class PropertySettingsScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const PropertySettingsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertySettingsScreen> createState() => _PropertySettingsScreenState();
}

class _PropertySettingsScreenState extends ConsumerState<PropertySettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertyDetailAsync = ref.watch(propertyDetailProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: propertyDetailAsync.when(
          data: (property) => Text('${property?.name ?? 'Property'} Settings'),
          loading: () => const Text('Property Settings'),
          error: (_, __) => const Text('Property Settings'),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _refreshPropertyData(),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Data',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Systems'),
            Tab(icon: Icon(Icons.edit_note), text: 'Content'),
            Tab(icon: Icon(Icons.settings), text: 'Config'),
            Tab(icon: Icon(Icons.security), text: 'Access'),
          ],
        ),
      ),
      body: propertyDetailAsync.when(
        data: (property) {
          if (property == null) {
            return _buildPropertyNotFound();
          }
          return TabBarView(
            controller: _tabController,
            children: [
              _buildSystemsTab(property),
              _buildContentTab(property),
              _buildConfigTab(property),
              _buildAccessTab(property),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildPropertyNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Property not found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading property settings',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemsTab(PropertyDetail property) {
    final systemConfigsAsync = ref.watch(propertySystemConfigsProvider(widget.propertyId));

    return systemConfigsAsync.when(
      data: (configs) => _buildSystemsList(configs),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, color: Colors.red[400], size: 48),
            const SizedBox(height: 16),
            Text('Error loading systems: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(propertySystemConfigsProvider(widget.propertyId)),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemsList(List<PropertySystemConfig> configs) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: configs.length,
      itemBuilder: (context, index) {
        final config = configs[index];
        return _buildSystemCard(config);
      },
    );
  }

  Widget _buildSystemCard(PropertySystemConfig config) {
    IconData systemIcon;
    switch (config.systemType) {
      case 'WATER':
        systemIcon = Icons.water_drop;
        break;
      case 'ELECTRICITY':
        systemIcon = Icons.electrical_services;
        break;
      case 'SECURITY':
        systemIcon = Icons.security;
        break;
      case 'INTERNET':
        systemIcon = Icons.wifi;
        break;
      case 'OTT':
        systemIcon = Icons.tv;
        break;
      case 'MAINTENANCE':
        systemIcon = Icons.build;
        break;
      default:
        systemIcon = Icons.settings;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(systemIcon, color: Colors.blue[600]),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    config.displayName ?? config.systemType,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Switch(
                  value: config.isEnabled,
                  onChanged: (value) => _toggleSystemEnabled(config, value),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _editSystemConfig(config),
                    icon: const Icon(Icons.edit),
                    label: const Text('Configure'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToSystemScreen(config),
                    icon: const Icon(Icons.open_in_new),
                    label: const Text('Open System'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentTab(PropertyDetail property) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info_outline, size: 64, color: Colors.blue[400]),
          const SizedBox(height: 16),
          Text(
            'Content Management',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Content editing is available directly in each system screen.\nUse the "Open System" button in the Systems tab to access content management.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Switch to Systems tab
              _tabController.animateTo(0);
            },
            icon: const Icon(Icons.dashboard),
            label: const Text('Go to Systems'),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigTab(PropertyDetail property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Property Configuration',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Basic Information
          _buildConfigSection(
            'Basic Information',
            [
              _buildConfigItem('Property Name', property.name, Icons.business),
              _buildConfigItem('Address', property.address, Icons.location_on),
              _buildConfigItem('Type', property.type, Icons.category),
              _buildConfigItem('Status', property.status, Icons.info),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Notification Settings
          _buildConfigSection(
            'Notifications',
            [
              _buildSwitchItem('Email Alerts', true, Icons.email),
              _buildSwitchItem('SMS Notifications', false, Icons.sms),
              _buildSwitchItem('Push Notifications', true, Icons.notifications),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Monitoring Settings
          _buildConfigSection(
            'Monitoring',
            [
              _buildConfigItem('Check Interval', '5 minutes', Icons.timer),
              _buildConfigItem('Alert Threshold', '85%', Icons.warning),
              _buildConfigItem('Maintenance Window', '2:00 AM - 4:00 AM', Icons.schedule),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccessTab(PropertyDetail property) {
    final propertyUsersAsync = ref.watch(propertyUsersProvider(widget.propertyId));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Access Control',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Assigned Users
          propertyUsersAsync.when(
            data: (users) => _buildUsersSection(users),
            loading: () => const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: CircularProgressIndicator(),
              ),
            ),
            error: (error, stack) => _buildUsersErrorState(error),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersSection(List<PropertyUser> users) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Assigned Users (${users.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.blue[700],
              ),
            ),
            ElevatedButton.icon(
              onPressed: () => _showAssignUserDialog(),
              icon: const Icon(Icons.person_add, size: 18),
              label: const Text('Assign User'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[700],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        if (users.isEmpty)
          _buildEmptyUsersState()
        else
          ...users.map((user) => _buildUserCard(user)),
      ],
    );
  }

  Widget _buildEmptyUsersState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.people_outline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 12),
          Text(
            'No users assigned',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Assign users to this property to manage access control',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUsersErrorState(dynamic error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[300]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: 12),
          Text(
            'Failed to load users',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.red[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.red[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => ref.invalidate(propertyUsersProvider(widget.propertyId)),
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[700],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.blue[700],
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildConfigItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchItem(String label, bool value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$label ${newValue ? 'enabled' : 'disabled'}'),
                  backgroundColor: newValue ? Colors.green : Colors.orange,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(PropertyUser user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: _getRoleColor(user.role),
              child: Icon(
                _getRoleIcon(user.role),
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.roleDisplayName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  if (user.email.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      user.email,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                  if (user.phone != null && user.phone!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.phone, size: 14, color: Colors.grey[500]),
                        const SizedBox(width: 4),
                        Text(
                          user.phone!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[500],
                          ),
                        ),
                        const SizedBox(width: 8),
                        InkWell(
                          onTap: () => _makePhoneCall(user.phone!),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.call, size: 12, color: Colors.green[700]),
                                const SizedBox(width: 4),
                                Text(
                                  'Call',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.green[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleUserAction(value, user),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 18),
                      SizedBox(width: 8),
                      Text('Edit Access'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'remove',
                  child: Row(
                    children: [
                      Icon(Icons.remove_circle, size: 18, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Remove Access', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              child: Icon(
                Icons.more_vert,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role.toUpperCase()) {
      case 'SUPER_ADMIN':
        return Colors.purple;
      case 'PROPERTY_MANAGER':
        return Colors.blue;
      case 'MAINTENANCE_STAFF':
        return Colors.orange;
      case 'SECURITY_PERSONNEL':
        return Colors.green;
      case 'TENANT':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData _getRoleIcon(String role) {
    switch (role.toUpperCase()) {
      case 'SUPER_ADMIN':
        return Icons.admin_panel_settings;
      case 'PROPERTY_MANAGER':
        return Icons.person;
      case 'MAINTENANCE_STAFF':
        return Icons.build;
      case 'SECURITY_PERSONNEL':
        return Icons.security;
      case 'TENANT':
        return Icons.home;
      default:
        return Icons.person;
    }
  }

  void _makePhoneCall(String phoneNumber) async {
    try {
      final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
      // Note: In a real app, you would use url_launcher package
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Calling $phoneNumber...'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to make call: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleUserAction(String action, PropertyUser user) {
    switch (action) {
      case 'edit':
        _showEditUserAccessDialog(user);
        break;
      case 'remove':
        _showRemoveUserDialog(user);
        break;
    }
  }

  void _showAssignUserDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('User assignment dialog coming soon'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showEditUserAccessDialog(PropertyUser user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit access for ${user.name} coming soon'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showRemoveUserDialog(PropertyUser user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove User Access'),
        content: Text('Are you sure you want to remove ${user.name} from this property?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _removeUserFromProperty(user);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _removeUserFromProperty(PropertyUser user) async {
    try {
      final repository = ref.read(propertyRepositoryProvider);
      final response = await repository.removeUserFromProperty(
        propertyId: widget.propertyId,
        userId: user.id,
      );

      if (response.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${user.name} removed from property'),
            backgroundColor: Colors.green,
          ),
        );
        // Refresh the users list
        ref.invalidate(propertyUsersProvider(widget.propertyId));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove user: ${response.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error removing user: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _refreshPropertyData() {
    // Clear all property-related cache and refresh
    final cacheManager = ref.read(propertyCacheManagerProvider);
    cacheManager.clearPropertyCache(widget.propertyId);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Property data refreshed'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _toggleSystemEnabled(PropertySystemConfig config, bool enabled) async {
    try {
      // TODO: Implement actual API call to update system status
      // For now, just show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${config.displayName ?? config.systemType} ${enabled ? 'enabled' : 'disabled'}'),
          backgroundColor: enabled ? Colors.green : Colors.orange,
          action: SnackBarAction(
            label: 'Undo',
            onPressed: () {
              // TODO: Implement undo functionality
            },
          ),
        ),
      );

      // Refresh the data to reflect changes
      ref.invalidate(propertySystemConfigsProvider(widget.propertyId));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update ${config.systemType}: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _editSystemConfig(PropertySystemConfig config) {
    showDialog(
      context: context,
      builder: (context) => _SystemConfigDialog(
        config: config,
        onSave: (updatedConfig) {
          // TODO: Implement actual save functionality
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${config.systemType} configuration updated'),
              backgroundColor: Colors.green,
            ),
          );
        },
      ),
    );
  }

  void _navigateToSystemScreen(PropertySystemConfig config) {
    // Navigate to the actual system screen where content management is available
    final systemRoute = _getSystemRoute(config.systemType);
    if (systemRoute != null) {
      final fullRoute = '/properties/${widget.propertyId}/$systemRoute';
      print('🔍 DEBUG: Navigating to system screen: $fullRoute');
      context.push(fullRoute);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${config.systemType} system screen not available yet'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  String? _getSystemRoute(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'WATER':
        return 'water';
      case 'ELECTRICITY':
        return 'electricity';
      case 'SECURITY':
        return 'security';
      case 'INTERNET':
        return 'internet';
      case 'OTT':
        return 'ott';
      case 'MAINTENANCE':
        return 'maintenance';
      default:
        print('🔍 DEBUG: Unknown system type: $systemType');
        return null;
    }
  }
}

class _SystemConfigDialog extends StatefulWidget {
  final PropertySystemConfig config;
  final Function(PropertySystemConfig) onSave;

  const _SystemConfigDialog({
    required this.config,
    required this.onSave,
  });

  @override
  State<_SystemConfigDialog> createState() => _SystemConfigDialogState();
}

class _SystemConfigDialogState extends State<_SystemConfigDialog> {
  late TextEditingController _displayNameController;
  late bool _isEnabled;
  late int _displayOrder;

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController(text: widget.config.displayName);
    _isEnabled = widget.config.isEnabled;
    _displayOrder = widget.config.displayOrder ?? 1;
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Configure ${widget.config.systemType}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Display Name
            TextField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // Enable/Disable
            SwitchListTile(
              title: const Text('Enable System'),
              subtitle: const Text('Allow this system to be used in the property'),
              value: _isEnabled,
              onChanged: (value) {
                setState(() {
                  _isEnabled = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Display Order
            TextField(
              decoration: const InputDecoration(
                labelText: 'Display Order',
                border: OutlineInputBorder(),
                helperText: 'Order in which this system appears (1-10)',
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                _displayOrder = int.tryParse(value) ?? 1;
              },
              controller: TextEditingController(text: _displayOrder.toString()),
            ),
            const SizedBox(height: 16),

            // Configuration Preview
            if (widget.config.configuration != null && widget.config.configuration!.isNotEmpty)
              ExpansionTile(
                title: const Text('Advanced Configuration'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _formatConfiguration(widget.config.configuration!),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveConfiguration,
          child: const Text('Save'),
        ),
      ],
    );
  }

  String _formatConfiguration(Map<String, dynamic> config) {
    try {
      return config.entries
          .map((e) => '${e.key}: ${e.value}')
          .join('\n');
    } catch (e) {
      return 'Configuration data available';
    }
  }

  void _saveConfiguration() {
    // Create updated config (for now, just close dialog)
    // TODO: Implement actual save to API
    Navigator.of(context).pop();
    widget.onSave(widget.config);
  }
}

import '../models/api_response.dart';
import '../../core/services/api_client.dart';
import '../../core/services/service_locator.dart';
import '../../core/constants/api_constants.dart';

class SystemRepository {
  final ApiClient _apiClient = serviceLocator.apiClient;

  Future<ApiResponse<List<Map<String, dynamic>>>> getSystemStatuses({
    String? propertyId,
    String? systemType,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{};

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (systemType != null) queryParams['systemType'] = systemType;
      if (status != null) queryParams['status'] = status;

      final response = await _apiClient.get(
        ApiConstants.systemStatusList,
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system statuses: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getSystemStatus(String id) async {
    try {
      final endpoint = ApiConstants.systemStatus.replaceAll('{id}', id);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system status: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> updateSystemStatus({
    required String id,
    required String status,
    String? description,
    int? healthScore,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final endpoint = ApiConstants.systemStatus.replaceAll('{id}', id);
      final response = await _apiClient.put(
        endpoint,
        data: {
          'status': status,
          if (description != null) 'description': description,
          if (healthScore != null) 'healthScore': healthScore,
          if (metadata != null) 'metadata': metadata,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update system status: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getSystemOverview({
    String? propertyId,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) queryParams['propertyId'] = propertyId;

      final response = await _apiClient.get(
        '${ApiConstants.systems}/overview',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system overview: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getSystemHealth({
    String? propertyId,
    String? timeRange,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (timeRange != null) queryParams['timeRange'] = timeRange;

      final response = await _apiClient.get(
        '${ApiConstants.systems}/health',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system health: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSystemAlerts({
    String? propertyId,
    String? systemType,
    String? severity,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (systemType != null) queryParams['systemType'] = systemType;
      if (severity != null) queryParams['severity'] = severity;

      final response = await _apiClient.get(
        '${ApiConstants.systems}/alerts',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system alerts: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> createSystemAlert({
    required String propertyId,
    required String systemType,
    required String severity,
    required String title,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _apiClient.post(
        '${ApiConstants.systems}/alerts',
        data: {
          'propertyId': propertyId,
          'systemType': systemType,
          'severity': severity,
          'title': title,
          'description': description,
          if (metadata != null) 'metadata': metadata,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to create system alert: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> acknowledgeSystemAlert(String alertId) async {
    try {
      final response = await _apiClient.post(
        '${ApiConstants.systems}/alerts/$alertId/acknowledge',
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to acknowledge system alert: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> resolveSystemAlert(String alertId) async {
    try {
      final response = await _apiClient.post(
        '${ApiConstants.systems}/alerts/$alertId/resolve',
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to resolve system alert: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSystemMetrics({
    required String propertyId,
    required String systemType,
    String? startDate,
    String? endDate,
    String? interval,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'propertyId': propertyId,
        'systemType': systemType,
      };

      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;
      if (interval != null) queryParams['interval'] = interval;

      final response = await _apiClient.get(
        '${ApiConstants.systems}/metrics',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system metrics: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> getSystemConfiguration({
    required String propertyId,
    required String systemType,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'propertyId': propertyId,
        'systemType': systemType,
      };

      final response = await _apiClient.get(
        '${ApiConstants.systems}/configuration',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch system configuration: $e');
    }
  }

  Future<ApiResponse<Map<String, dynamic>>> updateSystemConfiguration({
    required String propertyId,
    required String systemType,
    required Map<String, dynamic> configuration,
  }) async {
    try {
      final response = await _apiClient.put(
        '${ApiConstants.systems}/configuration',
        data: {
          'propertyId': propertyId,
          'systemType': systemType,
          'configuration': configuration,
        },
      );

      if (response.isSuccess) {
        return ApiResponse.success(data: response.data['data']);
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to update system configuration: $e');
    }
  }

  // Water system methods
  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterSystems({
    required String propertyId,
  }) async {
    try {
      final endpoint = ApiConstants.waterSystems.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch water systems: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterConsumption({
    required String propertyId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'propertyId': propertyId,
      };
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final response = await _apiClient.get(
        '${ApiConstants.systems}/water/consumption',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch water consumption: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterMaintenance({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/water/maintenance',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch water maintenance: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterQuality({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/water/quality',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch water quality: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getWaterContacts({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/water/contacts',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch water contacts: $e');
    }
  }

  // Electricity system methods
  Future<ApiResponse<List<Map<String, dynamic>>>> getElectricitySystems({
    required String propertyId,
  }) async {
    try {
      final endpoint = ApiConstants.electricitySystems.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch electricity systems: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getPowerAnalytics({
    required String propertyId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'propertyId': propertyId,
      };
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;

      final response = await _apiClient.get(
        '${ApiConstants.systems}/electricity/analytics',
        queryParameters: queryParams,
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch power analytics: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getElectricityMaintenance({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/electricity/maintenance',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch electricity maintenance: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getLoadManagement({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/electricity/load-management',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch load management: $e');
    }
  }

  // Security system methods
  Future<ApiResponse<List<Map<String, dynamic>>>> getSecuritySystems({
    required String propertyId,
  }) async {
    try {
      final endpoint = ApiConstants.securitySystems.replaceAll('{propertyId}', propertyId);
      final response = await _apiClient.get(endpoint);

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch security systems: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getAccessControl({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/security/access-control',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch access control: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSecurityIncidents({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/security/incidents',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch security incidents: $e');
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getSecurityMaintenance({
    required String propertyId,
  }) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.systems}/security/maintenance',
        queryParameters: {'propertyId': propertyId},
      );

      if (response.isSuccess) {
        final List<dynamic> data = response.data['data'] ?? [];
        return ApiResponse.success(data: data.cast<Map<String, dynamic>>());
      } else {
        return ApiResponse.error(error: response.message ?? 'Unknown error');
      }
    } catch (e) {
      return ApiResponse.error(error: 'Failed to fetch security maintenance: $e');
    }
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/employee_config_service.dart';
import '../../core/services/service_locator.dart';

// Service provider
final employeeConfigServiceProvider = Provider<EmployeeConfigService>((ref) {
  return getService<EmployeeConfigService>();
});

// Employee departments provider
final employeeDepartmentsProvider = FutureProvider<List<EmployeeDepartment>>((ref) async {
  final service = ref.read(employeeConfigServiceProvider);
  final response = await service.getDepartments();
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch departments');
  }
});

// Employee department by ID provider
final employeeDepartmentByIdProvider = FutureProvider.family<EmployeeDepartment, String>((ref, departmentId) async {
  final service = ref.read(employeeConfigServiceProvider);
  final response = await service.getDepartmentById(departmentId);
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch department');
  }
});

// Employee statuses provider
final employeeStatusesProvider = FutureProvider<List<EmployeeStatus>>((ref) async {
  final service = ref.read(employeeConfigServiceProvider);
  final response = await service.getEmployeeStatuses();
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch employee statuses');
  }
});

// Department management state providers
final departmentFormProvider = StateNotifierProvider<DepartmentFormNotifier, DepartmentFormState>((ref) {
  return DepartmentFormNotifier(ref.read(employeeConfigServiceProvider));
});

class DepartmentFormState {
  final bool isLoading;
  final String? error;
  final String? successMessage;
  final EmployeeDepartment? department;

  DepartmentFormState({
    this.isLoading = false,
    this.error,
    this.successMessage,
    this.department,
  });

  DepartmentFormState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
    EmployeeDepartment? department,
  }) {
    return DepartmentFormState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
      department: department ?? this.department,
    );
  }
}

class DepartmentFormNotifier extends StateNotifier<DepartmentFormState> {
  final EmployeeConfigService _service;

  DepartmentFormNotifier(this._service) : super(DepartmentFormState());

  Future<void> createDepartment({
    required String name,
    required String code,
    String? description,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.createDepartment(
        name: name,
        code: code,
        description: description,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Department created successfully',
          department: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create department',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create department: $e',
      );
    }
  }

  Future<void> updateDepartment({
    required String departmentId,
    String? name,
    String? code,
    String? description,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.updateDepartment(
        departmentId: departmentId,
        name: name,
        code: code,
        description: description,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Department updated successfully',
          department: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to update department',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update department: $e',
      );
    }
  }

  Future<void> deleteDepartment(String departmentId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.deleteDepartment(departmentId);

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Department deleted successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to delete department',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete department: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Employee status management state providers
final employeeStatusFormProvider = StateNotifierProvider<EmployeeStatusFormNotifier, EmployeeStatusFormState>((ref) {
  return EmployeeStatusFormNotifier(ref.read(employeeConfigServiceProvider));
});

class EmployeeStatusFormState {
  final bool isLoading;
  final String? error;
  final String? successMessage;
  final EmployeeStatus? status;

  EmployeeStatusFormState({
    this.isLoading = false,
    this.error,
    this.successMessage,
    this.status,
  });

  EmployeeStatusFormState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
    EmployeeStatus? status,
  }) {
    return EmployeeStatusFormState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
      status: status ?? this.status,
    );
  }
}

class EmployeeStatusFormNotifier extends StateNotifier<EmployeeStatusFormState> {
  final EmployeeConfigService _service;

  EmployeeStatusFormNotifier(this._service) : super(EmployeeStatusFormState());

  Future<void> createEmployeeStatus({
    required String name,
    required String code,
    String? description,
    bool? isActive,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.createEmployeeStatus(
        name: name,
        code: code,
        description: description,
        isActive: isActive,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Employee status created successfully',
          status: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create employee status',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create employee status: $e',
      );
    }
  }

  Future<void> updateEmployeeStatus({
    required String statusId,
    String? name,
    String? code,
    String? description,
    bool? isActive,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.updateEmployeeStatus(
        statusId: statusId,
        name: name,
        code: code,
        description: description,
        isActive: isActive,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Employee status updated successfully',
          status: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to update employee status',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update employee status: $e',
      );
    }
  }

  Future<void> deleteEmployeeStatus(String statusId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.deleteEmployeeStatus(statusId);

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Employee status deleted successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to delete employee status',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete employee status: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

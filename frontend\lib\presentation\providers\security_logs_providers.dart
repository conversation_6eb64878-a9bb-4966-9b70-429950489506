import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/security_logs_service.dart';
import '../../core/services/service_locator.dart';

// Service provider
final securityLogsServiceProvider = Provider<SecurityLogsService>((ref) {
  return getService<SecurityLogsService>();
});

// Security logs provider
final securityLogsProvider = FutureProvider.family<PaginatedSecurityLogs, SecurityLogsQuery>((ref, query) async {
  final service = ref.read(securityLogsServiceProvider);
  final response = await service.getSecurityLogs(
    propertyId: query.propertyId,
    startDate: query.startDate,
    endDate: query.endDate,
    status: query.status,
    guardName: query.guardName,
    page: query.page,
    limit: query.limit,
  );
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch security logs');
  }
});

// Security log by ID provider
final securityLogByIdProvider = FutureProvider.family<SecurityLog, String>((ref, logId) async {
  final service = ref.read(securityLogsServiceProvider);
  final response = await service.getSecurityLogById(logId);
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch security log');
  }
});

// Security log management state providers
final securityLogFormProvider = StateNotifierProvider<SecurityLogFormNotifier, SecurityLogFormState>((ref) {
  return SecurityLogFormNotifier(ref.read(securityLogsServiceProvider));
});

class SecurityLogFormState {
  final bool isLoading;
  final String? error;
  final String? successMessage;
  final SecurityLog? log;

  SecurityLogFormState({
    this.isLoading = false,
    this.error,
    this.successMessage,
    this.log,
  });

  SecurityLogFormState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
    SecurityLog? log,
  }) {
    return SecurityLogFormState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
      log: log ?? this.log,
    );
  }
}

class SecurityLogFormNotifier extends StateNotifier<SecurityLogFormState> {
  final SecurityLogsService _service;

  SecurityLogFormNotifier(this._service) : super(SecurityLogFormState());

  Future<void> createSecurityLog({
    required String propertyId,
    required String activity,
    required String location,
    required String guardName,
    required String status,
    String? notes,
    String? timestamp,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.createSecurityLog(
        propertyId: propertyId,
        activity: activity,
        location: location,
        guardName: guardName,
        status: status,
        notes: notes,
        timestamp: timestamp,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Security log created successfully',
          log: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create security log',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create security log: $e',
      );
    }
  }

  Future<void> updateSecurityLog({
    required String logId,
    String? activity,
    String? location,
    String? guardName,
    String? status,
    String? notes,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.updateSecurityLog(
        logId: logId,
        activity: activity,
        location: location,
        guardName: guardName,
        status: status,
        notes: notes,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Security log updated successfully',
          log: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to update security log',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update security log: $e',
      );
    }
  }

  Future<void> deleteSecurityLog(String logId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.deleteSecurityLog(logId);

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Security log deleted successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to delete security log',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete security log: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Query classes
class SecurityLogsQuery {
  final String? propertyId;
  final String? startDate;
  final String? endDate;
  final String? status;
  final String? guardName;
  final int page;
  final int limit;

  SecurityLogsQuery({
    this.propertyId,
    this.startDate,
    this.endDate,
    this.status,
    this.guardName,
    this.page = 1,
    this.limit = 20,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SecurityLogsQuery &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          status == other.status &&
          guardName == other.guardName &&
          page == other.page &&
          limit == other.limit;

  @override
  int get hashCode =>
      propertyId.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      status.hashCode ^
      guardName.hashCode ^
      page.hashCode ^
      limit.hashCode;
}

// Filter state providers
final securityLogsFilterProvider = StateProvider<SecurityLogsFilter>((ref) => SecurityLogsFilter());

class SecurityLogsFilter {
  final String? status;
  final String? guardName;
  final String? startDate;
  final String? endDate;

  SecurityLogsFilter({
    this.status,
    this.guardName,
    this.startDate,
    this.endDate,
  });

  SecurityLogsFilter copyWith({
    String? status,
    String? guardName,
    String? startDate,
    String? endDate,
  }) {
    return SecurityLogsFilter(
      status: status ?? this.status,
      guardName: guardName ?? this.guardName,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

// Filtered security logs provider
final filteredSecurityLogsProvider = Provider.family<AsyncValue<PaginatedSecurityLogs>, String?>((ref, propertyId) {
  final filter = ref.watch(securityLogsFilterProvider);
  
  final query = SecurityLogsQuery(
    propertyId: propertyId,
    status: filter.status,
    guardName: filter.guardName,
    startDate: filter.startDate,
    endDate: filter.endDate,
    page: 1,
    limit: 20,
  );
  
  return ref.watch(securityLogsProvider(query));
});

import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getMaintenanceAnalytics,
  getSecurityAnalytics
} from '../controllers/analyticsController';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Analytics routes
router.get('/maintenance', getMaintenanceAnalytics);
router.get('/security', getSecurityAnalytics);

export default router;

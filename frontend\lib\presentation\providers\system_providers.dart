import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/system.dart';
import '../../data/repositories/system_repository.dart';
import '../../core/services/service_locator.dart';
import '../../core/providers/cache_provider.dart';
import '../../core/utils/logger.dart';
import '../../core/providers/connectivity_provider.dart';


// System Repository Provider
final systemRepositoryProvider = Provider<SystemRepository>((ref) {
  return serviceLocator.systemRepository;
});

// System Statuses Provider with filtering
final systemStatusesProvider = FutureProvider.family<List<SystemStatus>, SystemStatusParams>((ref, params) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'system_statuses_${params.hashCode}';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isOnline) {
      final response = await repository.getSystemStatuses();

      if (response.isSuccess && response.data != null) {
        // Cache the fresh data
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 2), // Short cache for system status
        );
        return response.data!.map((item) => SystemStatus.fromJson(item)).toList();
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.getCachedData<List<dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData.map((item) => SystemStatus.fromJson(item)).toList();
    }

    // If no cache and offline, return empty list
    if (!connectivityManager.isOnline) {
      return [];
    }
    
    throw Exception('Failed to load system statuses');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.getCachedData<List<dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData.map((item) => SystemStatus.fromJson(item)).toList();
    }

    return [];
  }
});

// Water Systems Provider
final waterSystemsProvider = FutureProvider.family<WaterSystemsResponse, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'water_systems_$propertyId';

  try {
    // Try to get fresh data if connected
    if (connectivityManager.isOnline) {
      final response = await repository.getWaterSystems(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        // Convert raw data to WaterSystemsResponse
        final waterSystemsResponse = _convertToWaterSystemsResponse(response.data!);

        // Cache the fresh data
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5), // Medium cache for water systems
        );
        return waterSystemsResponse;
      }
    }

    // Fallback to cached data
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterSystemsResponse(cachedData);
    }

    // If no cache and offline, return empty response
    if (!connectivityManager.isOnline) {
      return WaterSystemsResponse.empty();
    }

    throw Exception('Failed to load water systems');

  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterSystemsResponse(cachedData);
    }

    return WaterSystemsResponse.empty();
  }
});

// Electricity Systems Provider
final electricitySystemsProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);
  
  final cacheKey = 'electricity_systems_$propertyId';
  
  try {
    // Try to get fresh data if connected
    if (connectivityManager.isOnline) {
      final response = await repository.getElectricitySystems(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        // Cache the fresh data
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5), // Medium cache for electricity systems
        );
        return response.data!;
      }
    }
    
    // Fallback to cached data
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    // If no cache and offline, return empty response
    if (!connectivityManager.isOnline) {
      return [];
    }
    
    throw Exception('Failed to load electricity systems');
    
  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

// Security Systems Provider
final securitySystemsProvider = FutureProvider.family<SecuritySystemsResponse, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'security_systems_$propertyId';

  try {
    // Try to get fresh data if connected
    if (connectivityManager.isOnline) {
      final response = await repository.getSecuritySystems(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        // Convert raw data to SecuritySystemsResponse
        final securitySystemsResponse = _convertToSecuritySystemsResponse(response.data!);

        // Cache the fresh data
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5), // Medium cache for security systems
        );
        return securitySystemsResponse;
      }
    }

    // Fallback to cached data
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToSecuritySystemsResponse(cachedData);
    }

    // If no cache and offline, return empty response
    if (!connectivityManager.isOnline) {
      return SecuritySystemsResponse.empty();
    }

    throw Exception('Failed to load security systems');

  } catch (e) {
    // Try cached data as last resort
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToSecuritySystemsResponse(cachedData);
    }

    return SecuritySystemsResponse.empty();
  }
});

// Update System Status Provider
final updateSystemStatusProvider = FutureProvider.family<bool, UpdateSystemStatusParams>((ref, params) async {
  final repository = ref.read(systemRepositoryProvider);
  
  try {
    final response = await repository.updateSystemStatus(
      id: params.id,
      status: params.status,
      description: params.description,
      healthScore: params.healthScore,
      metadata: params.metadata,
    );
    
    if (response.isSuccess) {
      // Invalidate related providers to refresh data
      ref.invalidate(systemStatusesProvider);
      ref.invalidate(waterSystemsProvider);
      ref.invalidate(electricitySystemsProvider);
      ref.invalidate(securitySystemsProvider);
      return true;
    }
    
    return false;
  } catch (e) {
    return false;
  }
});

// System Health Summary Provider (computed from all systems)
final systemHealthSummaryProvider = Provider.family<SystemHealthSummary, String>((ref, propertyId) {
  final waterSystemsAsyncValue = ref.watch(waterSystemsProvider(propertyId));
  final electricitySystemsAsyncValue = ref.watch(electricitySystemsProvider(propertyId));
  final securitySystemsAsyncValue = ref.watch(securitySystemsProvider(propertyId));

  return SystemHealthSummary(
    waterHealth: waterSystemsAsyncValue.when(
      data: (data) => _calculateWaterHealthFromResponse(data),
      loading: () => SystemHealth.unknown(),
      error: (_, __) => SystemHealth.unknown(),
    ),
    electricityHealth: electricitySystemsAsyncValue.when(
      data: (data) => _calculateElectricityHealth(data),
      loading: () => SystemHealth.unknown(),
      error: (_, __) => SystemHealth.unknown(),
    ),
    securityHealth: securitySystemsAsyncValue.when(
      data: (data) => _calculateSecurityHealthFromResponse(data),
      loading: () => SystemHealth.unknown(),
      error: (_, __) => SystemHealth.unknown(),
    ),
  );
});

// System Status Parameters
class SystemStatusParams {
  final String? propertyId;
  final String? systemType;
  final String? status;

  const SystemStatusParams({
    this.propertyId,
    this.systemType,
    this.status,
  });

  @override
  int get hashCode => Object.hash(propertyId, systemType, status);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SystemStatusParams &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          systemType == other.systemType &&
          status == other.status;
}

// Update System Status Parameters
class UpdateSystemStatusParams {
  final String id;
  final String status;
  final String? description;
  final int? healthScore;
  final Map<String, dynamic>? metadata;

  const UpdateSystemStatusParams({
    required this.id,
    required this.status,
    this.description,
    this.healthScore,
    this.metadata,
  });
}

// System Health Summary Model
class SystemHealthSummary {
  final SystemHealth waterHealth;
  final SystemHealth electricityHealth;
  final SystemHealth securityHealth;

  const SystemHealthSummary({
    required this.waterHealth,
    required this.electricityHealth,
    required this.securityHealth,
  });

  int get overallHealthScore {
    final scores = [
      waterHealth.healthScore,
      electricityHealth.healthScore,
      securityHealth.healthScore,
    ].where((score) => score > 0);
    
    if (scores.isEmpty) return 0;
    return (scores.reduce((a, b) => a + b) / scores.length).round();
  }

  String get overallStatus {
    final statuses = [waterHealth.status, electricityHealth.status, securityHealth.status];
    
    if (statuses.contains('CRITICAL')) return 'CRITICAL';
    if (statuses.contains('WARNING')) return 'WARNING';
    if (statuses.contains('OPERATIONAL')) return 'OPERATIONAL';
    return 'UNKNOWN';
  }
}

// System Health Model
class SystemHealth {
  final String status;
  final int healthScore;
  final int operational;
  final int total;
  final String? lastUpdate;

  const SystemHealth({
    required this.status,
    required this.healthScore,
    required this.operational,
    required this.total,
    this.lastUpdate,
  });

  factory SystemHealth.unknown() {
    return const SystemHealth(
      status: 'UNKNOWN',
      healthScore: 0,
      operational: 0,
      total: 0,
    );
  }
}

// Helper functions to calculate system health
// TODO: Uncomment when needed for raw data processing
// SystemHealth _calculateWaterHealth(List<Map<String, dynamic>> data) {
//   if (data.isEmpty) return SystemHealth.unknown();

//   final operational = data.where((s) => s['pumpStatus'] == 'ON').length;
//   final total = data.length;
//   final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;

//   String status = 'OPERATIONAL';
//   if (healthScore < 50) {
//     status = 'CRITICAL';
//   } else if (healthScore < 80) {
//     status = 'WARNING';
//   }

//   return SystemHealth(
//     status: status,
//     healthScore: healthScore,
//     operational: operational,
//     total: total,
//     lastUpdate: DateTime.now().toIso8601String(),
//   );
// }

SystemHealth _calculateElectricityHealth(List<Map<String, dynamic>> data) {
  if (data.isEmpty) return SystemHealth.unknown();

  final operational = data.where((s) =>
    s['generatorStatus'] == 'ON' || s['mainsPowerStatus'] == 'AVAILABLE'
  ).length;
  final total = data.length;
  final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;

  String status = 'OPERATIONAL';
  if (healthScore < 50) {
    status = 'CRITICAL';
  } else if (healthScore < 80) {
    status = 'WARNING';
  }

  return SystemHealth(
    status: status,
    healthScore: healthScore,
    operational: operational,
    total: total,
    lastUpdate: DateTime.now().toIso8601String(),
  );
}

// SystemHealth _calculateSecurityHealth(List<Map<String, dynamic>> data) {
//   if (data.isEmpty) return SystemHealth.unknown();

//   final operational = data.where((s) => s['alarmStatus'] == 'ARMED').length;
//   final total = data.length;
//   final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;

//   String status = 'OPERATIONAL';
//   if (healthScore < 50) {
//     status = 'CRITICAL';
//   } else if (healthScore < 80) {
//     status = 'WARNING';
//   }

//   return SystemHealth(
//     status: status,
//     healthScore: healthScore,
//     operational: operational,
//     total: total,
//     lastUpdate: DateTime.now().toIso8601String(),
//   );
// }

// Helper functions to calculate system health from response objects
SystemHealth _calculateWaterHealthFromResponse(WaterSystemsResponse response) {
  if (response.systems.isEmpty) return SystemHealth.unknown();

  final operational = response.systems.where((s) => s.isPumpOn).length;
  final total = response.systems.length;
  final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;

  String status = 'OPERATIONAL';
  if (healthScore < 50) {
    status = 'CRITICAL';
  } else if (healthScore < 80) {
    status = 'WARNING';
  }

  return SystemHealth(
    status: status,
    healthScore: healthScore,
    operational: operational,
    total: total,
    lastUpdate: DateTime.now().toIso8601String(),
  );
}

SystemHealth _calculateSecurityHealthFromResponse(SecuritySystemsResponse response) {
  if (response.systems.isEmpty) return SystemHealth.unknown();

  final operational = response.systems.where((s) => s.isAlarmArmed).length;
  final total = response.systems.length;
  final healthScore = total > 0 ? ((operational / total) * 100).round() : 0;

  String status = 'OPERATIONAL';
  if (healthScore < 50) {
    status = 'CRITICAL';
  } else if (healthScore < 80) {
    status = 'WARNING';
  }

  return SystemHealth(
    status: status,
    healthScore: healthScore,
    operational: operational,
    total: total,
    lastUpdate: DateTime.now().toIso8601String(),
  );
}

// Water Consumption Provider
final waterConsumptionProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'water_consumption_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getWaterConsumption(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 1),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

// Water Maintenance Provider
final waterMaintenanceProvider = FutureProvider.family<WaterMaintenance, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'water_maintenance_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getWaterMaintenance(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        // Convert raw data to WaterMaintenance
        final waterMaintenance = _convertToWaterMaintenance(response.data!);

        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 2),
        );
        return waterMaintenance;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterMaintenance(cachedData);
    }

    return WaterMaintenance.empty();

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterMaintenance(cachedData);
    }

    return WaterMaintenance.empty();
  }
});

// Water Quality Provider
final waterQualityProvider = FutureProvider.family<WaterQuality, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'water_quality_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getWaterQuality(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        // Convert raw data to WaterQuality
        final waterQuality = _convertToWaterQuality(response.data!);

        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 6),
        );
        return waterQuality;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterQuality(cachedData);
    }

    return WaterQuality.empty();

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterQuality(cachedData);
    }

    return WaterQuality.empty();
  }
});

// Water Contacts Provider
final waterContactsProvider = FutureProvider.family<List<WaterContact>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'water_contacts_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getWaterContacts(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        final waterContacts = _convertToWaterContacts(response.data!);

        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 24), // Long cache for contacts
        );
        return waterContacts;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToWaterContacts(cachedData);
    }

    // No fallback data - return empty list if API fails
    return [];
  } catch (e) {
    Logger.error('Error in waterContactsProvider: $e');
    return [];
  }
});

// Security-specific providers
final accessControlProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'access_control_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getAccessControl(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 5),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

final securityIncidentsProvider = FutureProvider.family<SecurityIncidents, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'security_incidents_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getSecurityIncidents(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        // Convert raw data to SecurityIncidents
        final securityIncidents = _convertToSecurityIncidents(response.data!);

        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 10),
        );
        return securityIncidents;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToSecurityIncidents(cachedData);
    }

    return SecurityIncidents.empty();

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return _convertToSecurityIncidents(cachedData);
    }

    return SecurityIncidents.empty();
  }
});

final securityMaintenanceProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'security_maintenance_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getSecurityMaintenance(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 2),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

// Electricity-specific providers
final powerAnalyticsProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'power_analytics_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getPowerAnalytics(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 1),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

final electricityMaintenanceProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'electricity_maintenance_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getElectricityMaintenance(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(hours: 2),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

final loadManagementProvider = FutureProvider.family<List<Map<String, dynamic>>, String>((ref, propertyId) async {
  final repository = ref.read(systemRepositoryProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  final connectivityManager = ref.read(connectivityManagerProvider);

  final cacheKey = 'load_management_$propertyId';

  try {
    if (connectivityManager.isOnline) {
      final response = await repository.getLoadManagement(propertyId: propertyId);

      if (response.isSuccess && response.data != null) {
        await cacheManager.cacheData(
          cacheKey,
          response.data!,
          duration: const Duration(minutes: 30),
        );
        return response.data!;
      }
    }

    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];

  } catch (e) {
    final cachedData = await cacheManager.getCachedData<List<Map<String, dynamic>>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    return [];
  }
});

// Helper functions to convert raw data to proper response objects
SecuritySystemsResponse _convertToSecuritySystemsResponse(List<Map<String, dynamic>> data) {
  try {
    // Convert each item to SecuritySystem (from system.dart)
    final systems = data.map((item) => SecuritySystem(
      id: item['id'] ?? '',
      propertyId: item['propertyId'] ?? '',
      systemName: item['systemName'] ?? item['name'] ?? '',
      cameraCount: item['cameraCount'] ?? 0,
      activeCameras: item['activeCameras'] ?? 0,
      accessPoints: item['accessPoints'] ?? 0,
      activeAccess: item['activeAccess'] ?? 0,
      alarmStatus: item['alarmStatus'] ?? 'DISARMED',
      motionDetected: item['motionDetected'] ?? false,
      lastIncident: item['lastIncident'],
      lastMaintenance: item['lastMaintenance'],
      nextMaintenance: item['nextMaintenance'],
      isActive: item['isActive'] ?? true,
      createdAt: item['createdAt'] ?? DateTime.now().toIso8601String(),
      updatedAt: item['updatedAt'] ?? DateTime.now().toIso8601String(),
      property: null,
      cameras: [],
    )).toList();

    // Calculate summary from systems
    final summary = SecuritySystemsSummary(
      totalSystems: systems.length,
      totalCameras: systems.fold(0, (sum, system) => sum + system.cameraCount),
      totalActiveCameras: systems.fold(0, (sum, system) => sum + system.activeCameras),
      totalAccessPoints: systems.fold(0, (sum, system) => sum + system.accessPoints),
      totalActiveAccess: systems.fold(0, (sum, system) => sum + system.activeAccess),
      armedSystems: systems.where((s) => s.isAlarmArmed).length,
      triggeredAlarms: systems.where((s) => s.isAlarmTriggered).length,
      maintenanceRequired: systems.where((s) => s.isAlarmMaintenance).length,
      cameraOperationalRate: systems.isEmpty ? 0 :
        (systems.fold(0.0, (sum, system) => sum + system.cameraOperationalRate) / systems.length).round(),
      accessOperationalRate: systems.isEmpty ? 0 :
        (systems.fold(0.0, (sum, system) => sum + system.accessOperationalRate) / systems.length).round(),
    );

    return SecuritySystemsResponse(systems: systems, summary: summary);
  } catch (e) {
    print('Error converting to SecuritySystemsResponse: $e');
    return SecuritySystemsResponse.empty();
  }
}

WaterSystemsResponse _convertToWaterSystemsResponse(List<Map<String, dynamic>> data) {
  try {
    // Convert each item to WaterSystem (from system.dart)
    final systems = data.map((item) => WaterSystem(
      id: item['id'] ?? '',
      propertyId: item['propertyId'] ?? '',
      tankName: item['tankName'] ?? item['name'] ?? '',
      capacity: (item['capacity'] ?? 0).toDouble(),
      currentLevel: (item['currentLevel'] ?? 0).toDouble(),
      levelPercentage: (item['levelPercentage'] ?? 0).toDouble(),
      pumpStatus: item['pumpStatus'] ?? 'OFF',
      flowRate: item['flowRate']?.toDouble(),
      pressure: item['pressure']?.toDouble(),
      quality: item['quality'],
      lastMaintenance: item['lastMaintenance'],
      nextMaintenance: item['nextMaintenance'],
      isActive: item['isActive'] ?? true,
      createdAt: item['createdAt'] ?? DateTime.now().toIso8601String(),
      updatedAt: item['updatedAt'] ?? DateTime.now().toIso8601String(),
      property: null,
    )).toList();

    // Calculate summary from systems
    final summary = WaterSystemsSummary(
      totalSystems: systems.length,
      totalCapacity: systems.fold(0.0, (sum, system) => sum + system.capacity),
      totalCurrentLevel: systems.fold(0.0, (sum, system) => sum + system.currentLevel),
      averageLevel: systems.isEmpty ? 0 :
        (systems.fold(0.0, (sum, system) => sum + system.levelPercentage) / systems.length).round(),
      activePumps: systems.where((s) => s.isPumpOn).length,
      maintenanceRequired: systems.where((s) => s.isPumpMaintenance).length,
    );

    return WaterSystemsResponse(systems: systems, summary: summary);
  } catch (e) {
    print('Error converting to WaterSystemsResponse: $e');
    return WaterSystemsResponse.empty();
  }
}

WaterMaintenance _convertToWaterMaintenance(List<Map<String, dynamic>> data) {
  try {
    // Convert each item to MaintenanceTask
    final tasks = data.map((item) => MaintenanceTask(
      id: item['id'] ?? '',
      title: item['title'] ?? '',
      description: item['description'] ?? '',
      type: item['type'] ?? '',
      dueDate: item['dueDate'] != null ? DateTime.parse(item['dueDate']) : DateTime.now(),
      priority: item['priority'] ?? 'medium',
      status: item['status'] ?? 'pending',
      frequency: item['frequency'],
      lastCompleted: item['lastCompleted'] != null ? DateTime.parse(item['lastCompleted']) : null,
    )).toList();

    return WaterMaintenance(
      overdueTasks: data.where((item) => item['status'] == 'overdue').length,
      dueSoonTasks: data.where((item) => item['status'] == 'due_soon').length,
      completedTasks: data.where((item) => item['status'] == 'completed').length,
      upcomingMaintenance: tasks.where((task) => task.status == 'pending' || task.status == 'due_soon').toList(),
      maintenanceHistory: [], // Would be populated from actual data
      tasks: tasks,
    );
  } catch (e) {
    print('Error converting to WaterMaintenance: $e');
    return WaterMaintenance.empty();
  }
}

WaterQuality _convertToWaterQuality(List<Map<String, dynamic>> data) {
  try {
    // For now, create a simple WaterQuality object from the data
    // In a real implementation, this would parse the actual quality data structure
    return WaterQuality(
      overallRating: data.isNotEmpty ? (data.first['rating'] ?? 'GOOD') : 'UNKNOWN',
      lastTested: data.isNotEmpty && data.first['lastTested'] != null
        ? DateTime.parse(data.first['lastTested'])
        : DateTime.now(),
      alerts: data.where((item) => item['alert'] == true).map((item) => item['message'] as String? ?? '').toList(),
      parameters: [], // Would be populated from actual data
      history: [], // Would be populated from actual data
    );
  } catch (e) {
    print('Error converting to WaterQuality: $e');
    return WaterQuality.empty();
  }
}

SecurityIncidents _convertToSecurityIncidents(List<Map<String, dynamic>> data) {
  try {
    // Convert each item to SecurityIncident
    final incidents = data.map((item) => SecurityIncident(
      id: item['id'] ?? '',
      title: item['title'] ?? '',
      description: item['description'] ?? '',
      type: item['type'] ?? 'general',
      severity: item['severity'] ?? 'low',
      status: item['status'] ?? 'open',
      location: item['location'] ?? '',
      timestamp: item['timestamp'] != null ? DateTime.parse(item['timestamp']) : DateTime.now(),
      reportedBy: item['reportedBy'],
      resolvedAt: item['resolvedAt'] != null ? DateTime.parse(item['resolvedAt']) : null,
    )).toList();

    // Calculate statistics
    final statistics = IncidentStatistics(
      byType: _groupByField(data, 'type'),
      bySeverity: _groupByField(data, 'severity'),
      byStatus: _groupByField(data, 'status'),
    );

    return SecurityIncidents(
      openIncidents: data.where((item) => item['status'] == 'open').length,
      todayIncidents: data.where((item) {
        final timestamp = item['timestamp'];
        if (timestamp == null) return false;
        final date = DateTime.parse(timestamp);
        final today = DateTime.now();
        return date.year == today.year && date.month == today.month && date.day == today.day;
      }).length,
      weeklyResolved: data.where((item) => item['status'] == 'resolved').length,
      recentIncidents: incidents.take(10).toList(),
      statistics: statistics,
    );
  } catch (e) {
    print('Error converting to SecurityIncidents: $e');
    return SecurityIncidents.empty();
  }
}

Map<String, int> _groupByField(List<Map<String, dynamic>> data, String field) {
  final Map<String, int> result = {};
  for (final item in data) {
    final value = item[field] as String? ?? 'unknown';
    result[value] = (result[value] ?? 0) + 1;
  }
  return result;
}

List<WaterContact> _convertToWaterContacts(List<Map<String, dynamic>> data) {
  try {
    return data.map((item) => WaterContact(
      id: item['id'] ?? '',
      name: item['name'] ?? '',
      organization: item['organization'] ?? '',
      phone: item['phone'] ?? '',
      email: item['email'] ?? '',
      type: item['type'] ?? 'general',
    )).toList();
  } catch (e) {
    Logger.error('Error converting to WaterContacts: $e');
    return [];
  }
}

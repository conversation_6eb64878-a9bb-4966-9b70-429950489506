// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'construction_site.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConstructionSite _$ConstructionSiteFromJson(Map<String, dynamic> json) =>
    ConstructionSite(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
      projectManager: json['projectManager'] as String,
      contractor: json['contractor'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      expectedEndDate: json['expectedEndDate'] == null
          ? null
          : DateTime.parse(json['expectedEndDate'] as String),
      actualEndDate: json['actualEndDate'] == null
          ? null
          : DateTime.parse(json['actualEndDate'] as String),
      budget: (json['budget'] as num).toDouble(),
      spentAmount: (json['spentAmount'] as num).toDouble(),
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$ConstructionSiteToJson(ConstructionSite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'location': instance.location,
      'status': instance.status,
      'priority': instance.priority,
      'projectManager': instance.projectManager,
      'contractor': instance.contractor,
      'startDate': instance.startDate.toIso8601String(),
      'expectedEndDate': instance.expectedEndDate?.toIso8601String(),
      'actualEndDate': instance.actualEndDate?.toIso8601String(),
      'budget': instance.budget,
      'spentAmount': instance.spentAmount,
      'progressPercentage': instance.progressPercentage,
      'attachments': instance.attachments,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

ConstructionSiteAttendance _$ConstructionSiteAttendanceFromJson(
        Map<String, dynamic> json) =>
    ConstructionSiteAttendance(
      id: json['id'] as String,
      siteId: json['siteId'] as String,
      workerId: json['workerId'] as String,
      workerName: json['workerName'] as String,
      workerRole: json['workerRole'] as String,
      checkInTime: DateTime.parse(json['checkInTime'] as String),
      checkOutTime: json['checkOutTime'] == null
          ? null
          : DateTime.parse(json['checkOutTime'] as String),
      hoursWorked: (json['hoursWorked'] as num?)?.toInt(),
      status: json['status'] as String,
      notes: json['notes'] as String?,
      date: json['date'] as String,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$ConstructionSiteAttendanceToJson(
        ConstructionSiteAttendance instance) =>
    <String, dynamic>{
      'id': instance.id,
      'siteId': instance.siteId,
      'workerId': instance.workerId,
      'workerName': instance.workerName,
      'workerRole': instance.workerRole,
      'checkInTime': instance.checkInTime.toIso8601String(),
      'checkOutTime': instance.checkOutTime?.toIso8601String(),
      'hoursWorked': instance.hoursWorked,
      'status': instance.status,
      'notes': instance.notes,
      'date': instance.date,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

PaginatedConstructionSites _$PaginatedConstructionSitesFromJson(
        Map<String, dynamic> json) =>
    PaginatedConstructionSites(
      data: (json['data'] as List<dynamic>)
          .map((e) => ConstructionSite.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: (json['total'] as num).toInt(),
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
    );

Map<String, dynamic> _$PaginatedConstructionSitesToJson(
        PaginatedConstructionSites instance) =>
    <String, dynamic>{
      'data': instance.data,
      'total': instance.total,
      'page': instance.page,
      'limit': instance.limit,
      'totalPages': instance.totalPages,
    };

PaginatedConstructionSiteAttendance
    _$PaginatedConstructionSiteAttendanceFromJson(Map<String, dynamic> json) =>
        PaginatedConstructionSiteAttendance(
          data: (json['data'] as List<dynamic>)
              .map((e) => ConstructionSiteAttendance.fromJson(
                  e as Map<String, dynamic>))
              .toList(),
          total: (json['total'] as num).toInt(),
          page: (json['page'] as num).toInt(),
          limit: (json['limit'] as num).toInt(),
          totalPages: (json['totalPages'] as num).toInt(),
        );

Map<String, dynamic> _$PaginatedConstructionSiteAttendanceToJson(
        PaginatedConstructionSiteAttendance instance) =>
    <String, dynamic>{
      'data': instance.data,
      'total': instance.total,
      'page': instance.page,
      'limit': instance.limit,
      'totalPages': instance.totalPages,
    };

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const createDepartmentSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  code: z.string().min(1),
});

const updateDepartmentSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  code: z.string().min(1).optional(),
});

const createStatusSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  code: z.string().min(1),
  isActive: z.boolean().optional(),
});

const updateStatusSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  code: z.string().min(1).optional(),
  isActive: z.boolean().optional(),
});

// Get all departments
export const getDepartments = async (req: Request, res: Response) => {
  try {
    const departments = await prisma.employeeDepartment.findMany({
      include: {
        _count: {
          select: {
            employees: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    const formattedDepartments = departments.map(dept => ({
      id: dept.id,
      name: dept.name,
      description: dept.description,
      code: dept.code,
      employeeCount: dept._count.employees,
      createdAt: dept.createdAt,
      updatedAt: dept.updatedAt
    }));

    res.json({
      success: true,
      data: formattedDepartments
    });
  } catch (error) {
    console.error('Error fetching departments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch departments'
    });
  }
};

// Get department by ID
export const getDepartmentById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const department = await prisma.employeeDepartment.findUnique({
      where: { id },
      include: {
        employees: {
          select: {
            id: true,
            name: true,
            email: true,
            position: true,
            status: true
          }
        },
        _count: {
          select: {
            employees: true
          }
        }
      }
    });

    if (!department) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }

    res.json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('Error fetching department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch department'
    });
  }
};

// Create department
export const createDepartment = async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;
    
    // Only admins can create departments
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const validatedData = createDepartmentSchema.parse(req.body);

    // Check if department code already exists
    const existingDepartment = await prisma.employeeDepartment.findUnique({
      where: { code: validatedData.code }
    });

    if (existingDepartment) {
      return res.status(400).json({
        success: false,
        message: 'Department with this code already exists'
      });
    }

    const department = await prisma.employeeDepartment.create({
      data: validatedData
    });

    res.status(201).json({
      success: true,
      data: department,
      message: 'Department created successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error creating department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create department'
    });
  }
};

// Update department
export const updateDepartment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const validatedData = updateDepartmentSchema.parse(req.body);

    // Check if department exists
    const existingDepartment = await prisma.employeeDepartment.findUnique({
      where: { id }
    });

    if (!existingDepartment) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }

    // Check if new code conflicts with existing department
    if (validatedData.code && validatedData.code !== existingDepartment.code) {
      const codeConflict = await prisma.employeeDepartment.findUnique({
        where: { code: validatedData.code }
      });

      if (codeConflict) {
        return res.status(400).json({
          success: false,
          message: 'Department with this code already exists'
        });
      }
    }

    const updatedDepartment = await prisma.employeeDepartment.update({
      where: { id },
      data: validatedData
    });

    res.json({
      success: true,
      data: updatedDepartment,
      message: 'Department updated successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error updating department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update department'
    });
  }
};

// Delete department
export const deleteDepartment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    // Check if department exists
    const existingDepartment = await prisma.employeeDepartment.findUnique({
      where: { id },
      include: {
        employees: true
      }
    });

    if (!existingDepartment) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }

    // Check if department has employees
    if (existingDepartment.employees.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete department that has employees assigned'
      });
    }

    await prisma.employeeDepartment.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Department deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting department:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete department'
    });
  }
};

// Get all employee statuses
export const getEmployeeStatuses = async (req: Request, res: Response) => {
  try {
    const statuses = await prisma.employeeStatus.findMany({
      include: {
        _count: {
          select: {
            employees: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    const formattedStatuses = statuses.map(status => ({
      id: status.id,
      name: status.name,
      description: status.description,
      code: status.code,
      isActive: status.isActive,
      employeeCount: status._count.employees,
      createdAt: status.createdAt,
      updatedAt: status.updatedAt
    }));

    res.json({
      success: true,
      data: formattedStatuses
    });
  } catch (error) {
    console.error('Error fetching employee statuses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employee statuses'
    });
  }
};

// Create employee status
export const createEmployeeStatus = async (req: Request, res: Response) => {
  try {
    const userRole = req.user?.role;
    
    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const validatedData = createStatusSchema.parse(req.body);

    // Check if status code already exists
    const existingStatus = await prisma.employeeStatus.findUnique({
      where: { code: validatedData.code }
    });

    if (existingStatus) {
      return res.status(400).json({
        success: false,
        message: 'Employee status with this code already exists'
      });
    }

    const status = await prisma.employeeStatus.create({
      data: {
        ...validatedData,
        isActive: validatedData.isActive ?? true
      }
    });

    res.status(201).json({
      success: true,
      data: status,
      message: 'Employee status created successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error creating employee status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create employee status'
    });
  }
};

// Update employee status
export const updateEmployeeStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;

    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const validatedData = updateStatusSchema.parse(req.body);

    // Check if status exists
    const existingStatus = await prisma.employeeStatus.findUnique({
      where: { id }
    });

    if (!existingStatus) {
      return res.status(404).json({
        success: false,
        message: 'Employee status not found'
      });
    }

    // Check if new code conflicts with existing status
    if (validatedData.code && validatedData.code !== existingStatus.code) {
      const codeConflict = await prisma.employeeStatus.findUnique({
        where: { code: validatedData.code }
      });

      if (codeConflict) {
        return res.status(400).json({
          success: false,
          message: 'Employee status with this code already exists'
        });
      }
    }

    const updatedStatus = await prisma.employeeStatus.update({
      where: { id },
      data: validatedData
    });

    res.json({
      success: true,
      data: updatedStatus,
      message: 'Employee status updated successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    console.error('Error updating employee status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update employee status'
    });
  }
};

// Delete employee status
export const deleteEmployeeStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userRole = req.user?.role;

    if (userRole !== 'SUPER_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    // Check if status exists
    const existingStatus = await prisma.employeeStatus.findUnique({
      where: { id },
      include: {
        employees: true
      }
    });

    if (!existingStatus) {
      return res.status(404).json({
        success: false,
        message: 'Employee status not found'
      });
    }

    // Check if status is assigned to employees
    if (existingStatus.employees.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete employee status that is assigned to employees'
      });
    }

    await prisma.employeeStatus.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Employee status deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting employee status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete employee status'
    });
  }
};

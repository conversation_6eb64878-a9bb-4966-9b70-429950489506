import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/security_logs_service.dart';
import '../../../data/models/system.dart';
import '../../providers/system_providers.dart';
import '../../providers/auth_providers.dart';
import '../../providers/security_logs_providers.dart';
import '../../widgets/enhanced_error_widget.dart';
import '../../widgets/retry_widget.dart';
import '../../widgets/offline_indicator.dart';
import '../main/main_navigation_screen.dart';

class SecurityManagementScreen extends ConsumerStatefulWidget {
  final String? propertyId;

  const SecurityManagementScreen({super.key, this.propertyId});

  @override
  ConsumerState<SecurityManagementScreen> createState() => _SecurityManagementScreenState();
}

class _SecurityManagementScreenState extends ConsumerState<SecurityManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userPermissions = ref.watch(userPermissionsProvider);
    
    // Check RBAC permissions
    if (!userPermissions.hasPermission('systems.security.view')) {
      return Scaffold(
        appBar: CustomAppBar(title: 'Security Management'),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Access Denied',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You don\'t have permission to view security management data',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Security Management',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSystemSettings,
          ),
          if (userPermissions.hasPermission('systems.security.control'))
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddSystemDialog,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          isScrollable: true,
          tabs: const [
            Tab(text: 'CCTV Monitoring'),
            Tab(text: 'Access Control'),
            Tab(text: 'Incidents'),
            Tab(text: 'Maintenance'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Offline Indicator
          const OfflineIndicator(),
          
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CCTVMonitoringTab(propertyId: widget.propertyId),
                AccessControlTab(propertyId: widget.propertyId),
                SecurityIncidentsTab(propertyId: widget.propertyId),
                SecurityMaintenanceTab(propertyId: widget.propertyId),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _refreshData() {
    ref.invalidate(securitySystemsProvider);
    ref.invalidate(securityIncidentsProvider);
    ref.invalidate(securityMaintenanceProvider);
  }

  void _showSystemSettings() {
    showDialog(
      context: context,
      builder: (context) => SecuritySystemSettingsDialog(propertyId: widget.propertyId),
    );
  }

  void _showAddSystemDialog() {
    showDialog(
      context: context,

      builder: (context) => AddSecuritySystemDialog(propertyId: widget.propertyId),
    );
  }
}

// CCTV Monitoring Tab
class CCTVMonitoringTab extends ConsumerWidget {
  final String? propertyId;

  const CCTVMonitoringTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final securitySystemsAsyncValue = ref.watch(securitySystemsProvider(propertyId ?? ''));

    return securitySystemsAsyncValue.when(
      data: (securitySystemsResponse) => _buildCCTVContent(context, ref, securitySystemsResponse),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildCCTVContent(BuildContext context, WidgetRef ref, SecuritySystemsResponse response) {
    if (response.systems.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(securitySystemsProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Cards
            _buildSummaryCards(context, response.summary),
            
            const SizedBox(height: 24),
            
            // CCTV Systems
            Text(
              'CCTV Systems',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...response.systems.map((system) => _buildCCTVSystemCard(context, system)),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, SecuritySystemsSummary summary) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Total Cameras',
            '${summary.totalActiveCameras}/${summary.totalCameras}',
            Icons.videocam,
            AppTheme.primaryColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'Operational Rate',
            '${summary.cameraOperationalRate}%',
            Icons.check_circle,
            summary.cameraOperationalRate >= 90 ? Colors.green : Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCCTVSystemCard(BuildContext context, SecuritySystem system) {
    final alarmStatusColor = _getAlarmStatusColor(system.alarmStatus);
    final cameraOperationalRate = system.cameraOperationalRate;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    system.systemName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: alarmStatusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: alarmStatusColor.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    system.alarmStatus,
                    style: TextStyle(
                      color: alarmStatusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Camera Status
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Camera Status',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            '${system.activeCameras}/${system.cameraCount} Online',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: cameraOperationalRate >= 90 ? Colors.green : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: cameraOperationalRate / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          cameraOperationalRate >= 90 ? Colors.green : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Camera Grid
            if (system.cameras.isNotEmpty) ...[
              Text(
                'Cameras',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: system.cameras.take(6).map((camera) => _buildCameraChip(camera)).toList(),
              ),
              
              if (system.cameras.length > 6) ...[
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () => _showAllCameras(context, system),
                  child: Text('View All ${system.cameras.length} Cameras'),
                ),
              ],
            ],
            
            const SizedBox(height: 16),
            
            // Additional Info
            Row(
              children: [
                Icon(Icons.security, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Access Points: ${system.activeAccess}/${system.accessPoints}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                
                if (system.motionDetected) ...[
                  Icon(Icons.motion_photos_on, size: 16, color: Colors.orange),
                  const SizedBox(width: 4),
                  Text(
                    'Motion Detected',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
            
            if (system.needsMaintenance) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Maintenance Required',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCameraChip(SecurityCamera camera) {
    final statusColor = _getCameraStatusColor(camera.status);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            camera.isOnline ? Icons.videocam : Icons.videocam_off,
            size: 12,
            color: statusColor,
          ),
          const SizedBox(width: 4),
          Text(
            camera.cameraName,
            style: TextStyle(
              fontSize: 10,
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return RetryWidget(
      onRetry: () async {
        ref.invalidate(securitySystemsProvider);
      },
      child: const SizedBox.shrink(),
      errorBuilder: (error, retryCount, retry) => EnhancedErrorWidget(
        error: error,
        onRetry: retry,
        title: 'Failed to load security systems',
        message: 'Please check your connection and try again.',
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.security_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No security systems found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Security systems will appear here when configured',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getAlarmStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ARMED':
        return Colors.green;
      case 'DISARMED':
        return Colors.grey;
      case 'TRIGGERED':
        return Colors.red;
      case 'MAINTENANCE':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getCameraStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ONLINE':
        return Colors.green;
      case 'OFFLINE':
        return Colors.red;
      case 'MAINTENANCE':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  void _showAllCameras(BuildContext context, SecuritySystem system) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${system.systemName} - All Cameras'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: system.cameras.length,
            itemBuilder: (context, index) {
              final camera = system.cameras[index];
              return ListTile(
                leading: Icon(
                  camera.isOnline ? Icons.videocam : Icons.videocam_off,
                  color: _getCameraStatusColor(camera.status),
                ),
                title: Text(camera.cameraName),
                subtitle: Text('${camera.location} - ${camera.status}'),
                trailing: camera.isRecording 
                    ? Icon(Icons.fiber_manual_record, color: Colors.red, size: 12)
                    : null,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Security Incidents Tab
class SecurityIncidentsTab extends ConsumerWidget {
  final String? propertyId;

  const SecurityIncidentsTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final incidentsAsyncValue = ref.watch(securityIncidentsProvider(propertyId ?? ''));

    return incidentsAsyncValue.when(
      data: (incidents) => _buildIncidentsContent(context, ref, incidents),
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildErrorState(context, ref, error),
    );
  }

  Widget _buildIncidentsContent(BuildContext context, WidgetRef ref, SecurityIncidents incidents) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(securityIncidentsProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Incidents Summary
            _buildIncidentsSummary(context, incidents),

            const SizedBox(height: 24),

            // Recent Incidents
            _buildRecentIncidents(context, incidents.recentIncidents),

            const SizedBox(height: 24),

            // Incident Statistics
            _buildIncidentStatistics(context, incidents.statistics),
          ],
        ),
      ),
    );
  }

  Widget _buildIncidentsSummary(BuildContext context, SecurityIncidents incidents) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Security Incidents Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildIncidentMetric(
                    'Open Incidents',
                    incidents.openIncidents.toString(),
                    Icons.warning,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildIncidentMetric(
                    'Today\'s Incidents',
                    incidents.todayIncidents.toString(),
                    Icons.today,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildIncidentMetric(
                    'Resolved This Week',
                    incidents.weeklyResolved.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncidentMetric(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRecentIncidents(BuildContext context, List<SecurityIncident> incidents) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Incidents',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _showReportIncidentDialog(context),
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Report Incident'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (incidents.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Text(
                    'No recent incidents',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              ...incidents.take(5).map((incident) => _buildIncidentItem(context, incident)),

            if (incidents.length > 5)
              TextButton(
                onPressed: () => _showAllIncidents(context, incidents),
                child: const Text('View All Incidents'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncidentItem(BuildContext context, SecurityIncident incident) {
    final severityColor = _getIncidentSeverityColor(incident.severity);
    final statusColor = _getIncidentStatusColor(incident.status);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getIncidentIcon(incident.type),
          color: severityColor,
        ),
        title: Text(incident.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(incident.description),
            const SizedBox(height: 4),
            Text('Location: ${incident.location}'),
            Text('Time: ${_formatDateTime(incident.timestamp)}'),
            if (incident.reportedBy != null)
              Text('Reported by: ${incident.reportedBy}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: severityColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: severityColor.withValues(alpha: 0.3)),
              ),
              child: Text(
                incident.severity,
                style: TextStyle(
                  color: severityColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor.withValues(alpha: 0.3)),
              ),
              child: Text(
                incident.status,
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        onTap: () => _showIncidentDetails(context, incident),
      ),
    );
  }

  Widget _buildIncidentStatistics(BuildContext context, IncidentStatistics statistics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Incident Statistics (Last 30 Days)',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            // Statistics by Type
            Text(
              'By Type',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),

            ...statistics.byType.entries.map((entry) => _buildStatisticItem(
              entry.key,
              entry.value.toString(),
              _getIncidentTypeColor(entry.key),
            )),

            const SizedBox(height: 16),

            // Statistics by Severity
            Text(
              'By Severity',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),

            ...statistics.bySeverity.entries.map((entry) => _buildStatisticItem(
              entry.key,
              entry.value.toString(),
              _getIncidentSeverityColor(entry.key),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticItem(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return EnhancedErrorWidget(
      error: error,
      onRetry: () => ref.invalidate(securityIncidentsProvider),
      title: 'Failed to load security incidents',
      message: 'Please check your connection and try again.',
    );
  }

  Color _getIncidentSeverityColor(String severity) {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return Colors.red;
      case 'HIGH':
        return Colors.orange;
      case 'MEDIUM':
        return Colors.yellow[700]!;
      case 'LOW':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getIncidentStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'OPEN':
        return Colors.red;
      case 'IN_PROGRESS':
        return Colors.orange;
      case 'RESOLVED':
        return Colors.green;
      case 'CLOSED':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getIncidentTypeColor(String type) {
    switch (type.toUpperCase()) {
      case 'INTRUSION':
        return Colors.red;
      case 'VANDALISM':
        return Colors.orange;
      case 'THEFT':
        return Colors.red[700]!;
      case 'SUSPICIOUS_ACTIVITY':
        return Colors.yellow[700]!;
      case 'EQUIPMENT_FAILURE':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getIncidentIcon(String type) {
    switch (type.toUpperCase()) {
      case 'INTRUSION':
        return Icons.person_off;
      case 'VANDALISM':
        return Icons.broken_image;
      case 'THEFT':
        return Icons.money_off;
      case 'SUSPICIOUS_ACTIVITY':
        return Icons.visibility;
      case 'EQUIPMENT_FAILURE':
        return Icons.build_circle;
      default:
        return Icons.warning;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showReportIncidentDialog(BuildContext context) {
    // Implementation for reporting new incident
  }

  void _showAllIncidents(BuildContext context, List<SecurityIncident> incidents) {
    // Implementation for showing all incidents
  }

  void _showIncidentDetails(BuildContext context, SecurityIncident incident) {
    // Implementation for showing incident details
  }
}

// Access Control Tab
class AccessControlTab extends ConsumerWidget {
  final String? propertyId;

  const AccessControlTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final securityLogsAsync = ref.watch(securityLogsProvider(SecurityLogsQuery(
      propertyId: propertyId,
      page: 1,
      limit: 20,
    )));

    return securityLogsAsync.when(
      data: (logs) => _buildAccessControlContent(context, ref, logs),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Error loading security logs: $error'),
            ElevatedButton(
              onPressed: () => ref.refresh(securityLogsProvider(SecurityLogsQuery(
                propertyId: propertyId,
                page: 1,
                limit: 20,
              ))),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessControlContent(BuildContext context, WidgetRef ref, PaginatedSecurityLogs logs) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.refresh(securityLogsProvider(SecurityLogsQuery(
          propertyId: propertyId,
          page: 1,
          limit: 20,
        )));
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with Add Button
            Row(
              children: [
                Text(
                  'Security Logs',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () => _showAddSecurityLogDialog(context, ref),
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('Add Log'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Security Logs List
            if (logs.data.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(
                        Icons.security_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No security logs found',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...logs.data.map((log) => _buildSecurityLogCard(context, ref, log)),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityLogCard(BuildContext context, WidgetRef ref, SecurityLog log) {
    final statusColor = _getLogStatusColor(log.status);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    log.activity,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    log.status,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Details
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  log.location,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  log.guardName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Timestamp
            Text(
              'Logged: ${_formatDateTime(log.timestamp)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
            ),

            if (log.notes?.isNotEmpty == true) ...[
              const SizedBox(height: 8),
              Text(
                'Notes: ${log.notes}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getLogStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'NORMAL':
        return Colors.green;
      case 'LOGGED':
        return Colors.blue;
      case 'COMPLETED':
        return Colors.green;
      case 'VERIFIED':
        return Colors.purple;
      case 'ALERT':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showAddSecurityLogDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement add security log dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add security log dialog - To be implemented')),
    );
  }
}

// Security Maintenance Tab
class SecurityMaintenanceTab extends ConsumerWidget {
  final String? propertyId;

  const SecurityMaintenanceTab({super.key, this.propertyId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Center(
      child: Text('Security Maintenance Tab - Coming Soon'),
    );
  }
}

// Security System Settings Dialog
class SecuritySystemSettingsDialog extends StatelessWidget {
  final String? propertyId;

  const SecuritySystemSettingsDialog({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Security System Settings'),
      content: const Text('Settings functionality coming soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

// Add Security System Dialog
class AddSecuritySystemDialog extends StatelessWidget {
  final String? propertyId;

  const AddSecuritySystemDialog({super.key, this.propertyId});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Security System'),
      content: const Text('Add system functionality coming soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';

class StatusIndicator extends StatelessWidget {
  final String status;
  final double size;
  final bool showText;

  const StatusIndicator({
    Key? key,
    required this.status,
    this.size = 12.0,
    this.showText = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor(status);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        if (showText) ...[
          const SizedBox(width: 4),
          Text(
            status,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'operational':
      case 'active':
      case 'online':
      case 'completed':
      case 'resolved':
        return Colors.green;
      case 'warning':
      case 'pending':
      case 'in_progress':
        return Colors.orange;
      case 'critical':
      case 'error':
      case 'offline':
      case 'failed':
        return Colors.red;
      case 'maintenance':
      case 'scheduled':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}

class StatusChip extends StatelessWidget {
  final String status;
  final String? label;

  const StatusChip({
    Key? key,
    required this.status,
    this.label,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor(status);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label ?? status,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'operational':
      case 'active':
      case 'online':
      case 'completed':
      case 'resolved':
        return Colors.green;
      case 'warning':
      case 'pending':
      case 'in_progress':
        return Colors.orange;
      case 'critical':
      case 'error':
      case 'offline':
      case 'failed':
        return Colors.red;
      case 'maintenance':
      case 'scheduled':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}

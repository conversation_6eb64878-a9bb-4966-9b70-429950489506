const { PrismaClient } = require('@prisma/client');

async function checkProperties() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking properties in database...');
    
    const properties = await prisma.property.findMany({
      select: {
        id: true,
        name: true,
        type: true,
        status: true,
      }
    });
    
    console.log(`📊 Found ${properties.length} properties:`);
    properties.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.name} (${prop.type}) - ${prop.status}`);
    });
    
    if (properties.length === 0) {
      console.log('❌ No properties found! Database might not be seeded.');
      console.log('💡 Try running: npm run db:seed');
    }
    
    // Check users too
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        role: true,
        assignedProperties: true,
      }
    });
    
    console.log(`\n👥 Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.role}) - Properties: ${user.assignedProperties.length}`);
    });
    
    // Check admin user specifically
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      console.log(`\n🔑 Admin user found: ${adminUser.email}`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   Assigned Properties: ${adminUser.assignedProperties.length}`);
      if (adminUser.assignedProperties.length === 0) {
        console.log('⚠️  Admin has no assigned properties!');
      }
    } else {
      console.log('\n❌ Admin user not found!');
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkProperties();

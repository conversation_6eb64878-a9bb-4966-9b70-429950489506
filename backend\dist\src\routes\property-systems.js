"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const client_1 = require("@prisma/client");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const zod_1 = require("zod");
const router = (0, express_1.Router)();
const prisma = new client_1.PrismaClient();
// Validation schemas
const systemConfigSchema = zod_1.z.object({
    systemType: zod_1.z.nativeEnum(client_1.SystemType),
    isEnabled: zod_1.z.boolean(),
    displayName: zod_1.z.string().optional(),
    displayOrder: zod_1.z.number().optional(),
    configuration: zod_1.z.record(zod_1.z.any()).optional(),
});
const systemContentSchema = zod_1.z.object({
    systemType: zod_1.z.nativeEnum(client_1.SystemType),
    contentType: zod_1.z.string(),
    title: zod_1.z.string(),
    content: zod_1.z.record(zod_1.z.any()),
    richContent: zod_1.z.string().optional(), // Large text content for rich text editor
    contentFormat: zod_1.z.enum(['markdown', 'html', 'json']).optional(), // Format of richContent
    displayOrder: zod_1.z.number().optional(),
    isActive: zod_1.z.boolean().default(true),
    isTab: zod_1.z.boolean().default(false), // Indicates if this content represents a tab
    tabIcon: zod_1.z.string().optional(), // Icon for tab
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const systemContactSchema = zod_1.z.object({
    systemType: zod_1.z.nativeEnum(client_1.SystemType),
    name: zod_1.z.string(),
    organization: zod_1.z.string().optional(),
    phone: zod_1.z.string(),
    email: zod_1.z.string().email().optional(),
    contactType: zod_1.z.string(),
    isActive: zod_1.z.boolean().default(true),
    displayOrder: zod_1.z.number().optional(),
});
// Get property system configurations
router.get('/:propertyId/configs', auth_1.authenticateToken, async (req, res) => {
    try {
        const { propertyId } = req.params;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const configs = await prisma.propertySystemConfig.findMany({
            where: { propertyId },
            orderBy: [
                { displayOrder: 'asc' },
                { systemType: 'asc' }
            ],
        });
        // If no configs exist, create default ones
        if (configs.length === 0) {
            const defaultSystems = [
                { systemType: client_1.SystemType.WATER, displayName: 'Water Management', displayOrder: 1 },
                { systemType: client_1.SystemType.ELECTRICITY, displayName: 'Electricity Management', displayOrder: 2 },
                { systemType: client_1.SystemType.SECURITY, displayName: 'Security Management', displayOrder: 3 },
                { systemType: client_1.SystemType.INTERNET, displayName: 'Internet Management', displayOrder: 4 },
                { systemType: client_1.SystemType.MAINTENANCE, displayName: 'Maintenance Management', displayOrder: 5 },
            ];
            const createdConfigs = await Promise.all(defaultSystems.map(system => prisma.propertySystemConfig.create({
                data: {
                    propertyId,
                    ...system,
                    isEnabled: true,
                    configuration: {},
                },
            })));
            return res.json({
                success: true,
                data: createdConfigs,
            });
        }
        res.json({
            success: true,
            data: configs,
        });
    }
    catch (error) {
        console.error('Error fetching property system configs:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update property system configuration
router.put('/:propertyId/configs/:configId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId, configId } = req.params;
        const user = req.user;
        const updateData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const updatedConfig = await prisma.propertySystemConfig.update({
            where: {
                id: configId,
                propertyId,
            },
            data: updateData,
        });
        res.json({
            success: true,
            data: updatedConfig,
        });
    }
    catch (error) {
        console.error('Error updating property system config:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Create system content
router.post('/:propertyId/content', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER, client_1.UserRole.SECURITY_PERSONNEL, client_1.UserRole.MAINTENANCE_STAFF]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId } = req.params;
        const user = req.user;
        const contentData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        // Role-based system type restrictions
        if (user.role === client_1.UserRole.SECURITY_PERSONNEL && contentData.systemType !== 'SECURITY') {
            return res.status(403).json({ error: 'Security personnel can only manage security system content' });
        }
        if (user.role === client_1.UserRole.MAINTENANCE_STAFF && !['MAINTENANCE', 'WATER', 'ELECTRICITY'].includes(contentData.systemType)) {
            return res.status(403).json({ error: 'Maintenance staff can only manage maintenance, water, and electricity system content' });
        }
        const content = await prisma.systemContent.create({
            data: {
                propertyId,
                ...contentData,
            },
        });
        res.status(201).json({
            success: true,
            data: content,
        });
    }
    catch (error) {
        console.error('Error creating system content:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get system content
router.get('/:propertyId/content', auth_1.authenticateToken, async (req, res) => {
    try {
        const { propertyId } = req.params;
        const { systemType, contentType } = req.query;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const whereClause = {
            propertyId,
            isActive: true,
        };
        if (systemType) {
            whereClause.systemType = systemType;
        }
        if (contentType) {
            whereClause.contentType = contentType;
        }
        const content = await prisma.systemContent.findMany({
            where: whereClause,
            orderBy: [
                { displayOrder: 'asc' },
                { createdAt: 'desc' }
            ],
        });
        res.json({
            success: true,
            data: content,
        });
    }
    catch (error) {
        console.error('Error fetching system content:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update system content
router.put('/:propertyId/content/:contentId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER, client_1.UserRole.SECURITY_PERSONNEL, client_1.UserRole.MAINTENANCE_STAFF]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId, contentId } = req.params;
        const user = req.user;
        const updateData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        // Get existing content to check system type
        const existingContent = await prisma.systemContent.findUnique({
            where: { id: contentId, propertyId },
        });
        if (!existingContent) {
            return res.status(404).json({ error: 'Content not found' });
        }
        // Role-based system type restrictions
        if (user.role === client_1.UserRole.SECURITY_PERSONNEL && existingContent.systemType !== 'SECURITY') {
            return res.status(403).json({ error: 'Security personnel can only manage security system content' });
        }
        if (user.role === client_1.UserRole.MAINTENANCE_STAFF && !['MAINTENANCE', 'WATER', 'ELECTRICITY'].includes(existingContent.systemType)) {
            return res.status(403).json({ error: 'Maintenance staff can only manage maintenance, water, and electricity system content' });
        }
        const updatedContent = await prisma.systemContent.update({
            where: {
                id: contentId,
                propertyId,
            },
            data: updateData,
        });
        res.json({
            success: true,
            data: updatedContent,
        });
    }
    catch (error) {
        console.error('Error updating system content:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Delete system content
router.delete('/:propertyId/content/:contentId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER, client_1.UserRole.SECURITY_PERSONNEL, client_1.UserRole.MAINTENANCE_STAFF]), async (req, res) => {
    try {
        const { propertyId, contentId } = req.params;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        // Get existing content to check system type
        const existingContent = await prisma.systemContent.findUnique({
            where: { id: contentId, propertyId },
        });
        if (!existingContent) {
            return res.status(404).json({ error: 'Content not found' });
        }
        // Role-based system type restrictions
        if (user.role === client_1.UserRole.SECURITY_PERSONNEL && existingContent.systemType !== 'SECURITY') {
            return res.status(403).json({ error: 'Security personnel can only manage security system content' });
        }
        if (user.role === client_1.UserRole.MAINTENANCE_STAFF && !['MAINTENANCE', 'WATER', 'ELECTRICITY'].includes(existingContent.systemType)) {
            return res.status(403).json({ error: 'Maintenance staff can only manage maintenance, water, and electricity system content' });
        }
        await prisma.systemContent.delete({
            where: {
                id: contentId,
                propertyId,
            },
        });
        res.json({
            success: true,
            message: 'Content deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting system content:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Create system contact
router.post('/:propertyId/contacts', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId } = req.params;
        const user = req.user;
        const contactData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const contact = await prisma.systemContact.create({
            data: {
                propertyId,
                ...contactData,
            },
        });
        res.status(201).json({
            success: true,
            data: contact,
        });
    }
    catch (error) {
        console.error('Error creating system contact:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get system contacts
router.get('/:propertyId/contacts', auth_1.authenticateToken, async (req, res) => {
    try {
        const { propertyId } = req.params;
        const { systemType } = req.query;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const whereClause = {
            propertyId,
            isActive: true,
        };
        if (systemType) {
            whereClause.systemType = systemType;
        }
        const contacts = await prisma.systemContact.findMany({
            where: whereClause,
            orderBy: [
                { displayOrder: 'asc' },
                { name: 'asc' }
            ],
        });
        res.json({
            success: true,
            data: contacts,
        });
    }
    catch (error) {
        console.error('Error fetching system contacts:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update system contact
router.put('/:propertyId/contacts/:contactId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId, contactId } = req.params;
        const user = req.user;
        const updateData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const updatedContact = await prisma.systemContact.update({
            where: {
                id: contactId,
                propertyId,
            },
            data: updateData,
        });
        res.json({
            success: true,
            data: updatedContact,
        });
    }
    catch (error) {
        console.error('Error updating system contact:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Delete system contact
router.delete('/:propertyId/contacts/:contactId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), async (req, res) => {
    try {
        const { propertyId, contactId } = req.params;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        await prisma.systemContact.delete({
            where: {
                id: contactId,
                propertyId,
            },
        });
        res.json({
            success: true,
            message: 'Contact deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting system contact:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get system tabs
router.get('/:propertyId/tabs/:systemType', auth_1.authenticateToken, async (req, res) => {
    try {
        const { propertyId, systemType } = req.params;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const tabs = await prisma.systemContent.findMany({
            where: {
                propertyId,
                systemType: systemType,
                isTab: true,
                isActive: true,
            },
            orderBy: [
                { displayOrder: 'asc' },
                { createdAt: 'asc' }
            ],
        });
        res.json({
            success: true,
            data: tabs,
        });
    }
    catch (error) {
        console.error('Error fetching system tabs:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Create system tab
router.post('/:propertyId/tabs', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId } = req.params;
        const user = req.user;
        const tabData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const tab = await prisma.systemContent.create({
            data: {
                propertyId,
                ...tabData,
                isTab: true,
            },
        });
        res.status(201).json({
            success: true,
            data: tab,
        });
    }
    catch (error) {
        console.error('Error creating system tab:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update system tab
router.put('/:propertyId/tabs/:tabId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), (0, validation_1.validateRequest)([]), async (req, res) => {
    try {
        const { propertyId, tabId } = req.params;
        const user = req.user;
        const updateData = req.body;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        const updatedTab = await prisma.systemContent.update({
            where: {
                id: tabId,
                propertyId,
                isTab: true,
            },
            data: updateData,
        });
        res.json({
            success: true,
            data: updatedTab,
        });
    }
    catch (error) {
        console.error('Error updating system tab:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Delete system tab
router.delete('/:propertyId/tabs/:tabId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), async (req, res) => {
    try {
        const { propertyId, tabId } = req.params;
        const user = req.user;
        // Check property access
        if (user.role !== client_1.UserRole.SUPER_ADMIN && !user.assignedProperties.includes(propertyId)) {
            return res.status(403).json({ error: 'Access denied to this property' });
        }
        await prisma.systemContent.delete({
            where: {
                id: tabId,
                propertyId,
                isTab: true,
            },
        });
        res.json({
            success: true,
            message: 'Tab deleted successfully',
        });
    }
    catch (error) {
        console.error('Error deleting system tab:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Copy system content from one property to another
router.post('/:sourcePropertyId/content/copy/:targetPropertyId', auth_1.authenticateToken, (0, auth_1.authorize)([client_1.UserRole.SUPER_ADMIN, client_1.UserRole.PROPERTY_MANAGER]), async (req, res) => {
    try {
        const { sourcePropertyId, targetPropertyId } = req.params;
        const user = req.user;
        const { systemTypes, overwriteExisting = false } = req.body;
        // Check access to both properties
        if (user.role !== client_1.UserRole.SUPER_ADMIN) {
            if (!user.assignedProperties.includes(sourcePropertyId) ||
                !user.assignedProperties.includes(targetPropertyId)) {
                return res.status(403).json({ error: 'Access denied to one or both properties' });
            }
        }
        // Verify both properties exist
        const [sourceProperty, targetProperty] = await Promise.all([
            prisma.property.findUnique({ where: { id: sourcePropertyId } }),
            prisma.property.findUnique({ where: { id: targetPropertyId } })
        ]);
        if (!sourceProperty || !targetProperty) {
            return res.status(404).json({ error: 'One or both properties not found' });
        }
        // Get source content
        const whereClause = {
            propertyId: sourcePropertyId,
            isActive: true,
        };
        if (systemTypes && Array.isArray(systemTypes) && systemTypes.length > 0) {
            whereClause.systemType = { in: systemTypes };
        }
        const sourceContent = await prisma.systemContent.findMany({
            where: whereClause,
            orderBy: [
                { systemType: 'asc' },
                { displayOrder: 'asc' },
                { createdAt: 'asc' }
            ]
        });
        let copiedCount = 0;
        let skippedCount = 0;
        let updatedCount = 0;
        for (const content of sourceContent) {
            // Check if content already exists in target
            const existingContent = await prisma.systemContent.findFirst({
                where: {
                    propertyId: targetPropertyId,
                    systemType: content.systemType,
                    contentType: content.contentType,
                    title: content.title,
                }
            });
            if (existingContent) {
                if (overwriteExisting) {
                    // Update existing content
                    await prisma.systemContent.update({
                        where: { id: existingContent.id },
                        data: {
                            content: content.content,
                            richContent: content.richContent,
                            contentFormat: content.contentFormat,
                            displayOrder: content.displayOrder,
                            isTab: content.isTab,
                            tabIcon: content.tabIcon,
                            metadata: content.metadata || {},
                        }
                    });
                    updatedCount++;
                }
                else {
                    skippedCount++;
                }
                continue;
            }
            // Create new content
            await prisma.systemContent.create({
                data: {
                    propertyId: targetPropertyId,
                    systemType: content.systemType,
                    contentType: content.contentType,
                    title: content.title,
                    content: content.content,
                    richContent: content.richContent,
                    contentFormat: content.contentFormat,
                    displayOrder: content.displayOrder,
                    isActive: content.isActive,
                    isTab: content.isTab,
                    tabIcon: content.tabIcon,
                    metadata: content.metadata || {},
                }
            });
            copiedCount++;
        }
        res.json({
            success: true,
            message: 'Content copied successfully',
            data: {
                sourceProperty: sourceProperty.name,
                targetProperty: targetProperty.name,
                totalProcessed: sourceContent.length,
                copied: copiedCount,
                updated: updatedCount,
                skipped: skippedCount,
            }
        });
    }
    catch (error) {
        console.error('Error copying system content:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.default = router;

import 'package:flutter/material.dart';
import '../../../core/services/service_locator.dart';
import '../../../data/repositories/property_system_repository.dart';
import '../../../data/models/system.dart';
import '../../../core/utils/api_response.dart';
import '../../widgets/rich_text_editor.dart';

class EnhancedSystemContentManagementScreen extends StatefulWidget {
  final String propertyId;
  final String systemType;
  final String systemName;

  const EnhancedSystemContentManagementScreen({
    super.key,
    required this.propertyId,
    required this.systemType,
    required this.systemName,
  });

  @override
  State<EnhancedSystemContentManagementScreen> createState() => _EnhancedSystemContentManagementScreenState();
}

class _EnhancedSystemContentManagementScreenState extends State<EnhancedSystemContentManagementScreen> 
    with TickerProviderStateMixin {
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;
  
  List<SystemContent> _tabs = [];
  List<SystemContent> _currentTabContent = [];
  bool _isLoading = true;
  String? _error;
  
  late TabController _tabController;
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadSystemTabs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemTabs() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load tabs for this system
      final tabsResponse = await _repository.getSystemContent(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        contentType: 'tab_content',
      );

      if (tabsResponse.isSuccess && tabsResponse.data != null) {
        final tabs = tabsResponse.data!.where((content) => content.isTab).toList();
        
        // If no tabs exist, create default tabs
        if (tabs.isEmpty) {
          await _createDefaultTabs();
          return _loadSystemTabs(); // Reload after creating defaults
        }

        setState(() {
          _tabs = tabs;
          _tabController = TabController(length: _tabs.length, vsync: this);
          _tabController.addListener(_onTabChanged);
          _isLoading = false;
        });

        // Load content for the first tab
        if (_tabs.isNotEmpty) {
          _loadTabContent(_tabs[0].id);
        }
      } else {
        setState(() {
          _error = tabsResponse.message ?? 'Failed to load system tabs';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading system tabs: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createDefaultTabs() async {
    final defaultTabs = _getDefaultTabsForSystem(widget.systemType);
    
    for (final tabData in defaultTabs) {
      await _repository.createSystemContent(
        propertyId: widget.propertyId,
        data: {
          'systemType': widget.systemType,
          'contentType': 'tab_content',
          'title': tabData['title'],
          'content': {},
          'richContent': tabData['defaultContent'],
          'contentFormat': 'markdown',
          'isTab': true,
          'tabIcon': tabData['icon'],
          'displayOrder': tabData['order'],
        },
      );
    }
  }

  List<Map<String, dynamic>> _getDefaultTabsForSystem(String systemType) {
    switch (systemType.toLowerCase()) {
      case 'water':
        return [
          {'title': 'Overview', 'icon': 'water_drop', 'order': 1, 'defaultContent': '# Water System Overview\n\nWelcome to the water management system.'},
          {'title': 'Quality Monitoring', 'icon': 'analytics', 'order': 2, 'defaultContent': '# Water Quality Monitoring\n\nTrack water quality parameters here.'},
          {'title': 'Maintenance', 'icon': 'build', 'order': 3, 'defaultContent': '# Water System Maintenance\n\nMaintenance schedules and procedures.'},
          {'title': 'Contacts', 'icon': 'contact_phone', 'order': 4, 'defaultContent': '# Emergency Contacts\n\nImportant contact information.'},
        ];
      case 'electricity':
        return [
          {'title': 'Overview', 'icon': 'electrical_services', 'order': 1, 'defaultContent': '# Electricity System Overview\n\nElectrical system management dashboard.'},
          {'title': 'Consumption', 'icon': 'analytics', 'order': 2, 'defaultContent': '# Power Consumption\n\nMonitor electricity usage and costs.'},
          {'title': 'Generator', 'icon': 'power', 'order': 3, 'defaultContent': '# Generator Management\n\nBackup power system information.'},
          {'title': 'Maintenance', 'icon': 'build', 'order': 4, 'defaultContent': '# Electrical Maintenance\n\nMaintenance schedules and safety procedures.'},
        ];
      case 'security':
        return [
          {'title': 'Overview', 'icon': 'security', 'order': 1, 'defaultContent': '# Security System Overview\n\nSecurity system status and controls.'},
          {'title': 'CCTV', 'icon': 'videocam', 'order': 2, 'defaultContent': '# CCTV Monitoring\n\nCamera feeds and recording management.'},
          {'title': 'Access Control', 'icon': 'key', 'order': 3, 'defaultContent': '# Access Control\n\nDoor locks and access management.'},
          {'title': 'Incidents', 'icon': 'report', 'order': 4, 'defaultContent': '# Security Incidents\n\nIncident reporting and tracking.'},
        ];
      default:
        return [
          {'title': 'Overview', 'icon': 'dashboard', 'order': 1, 'defaultContent': '# System Overview\n\nSystem management dashboard.'},
          {'title': 'Configuration', 'icon': 'settings', 'order': 2, 'defaultContent': '# Configuration\n\nSystem configuration and settings.'},
          {'title': 'Maintenance', 'icon': 'build', 'order': 3, 'defaultContent': '# Maintenance\n\nMaintenance information and schedules.'},
        ];
    }
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      final newIndex = _tabController.index;
      if (newIndex < _tabs.length) {
        setState(() {
          _selectedTabIndex = newIndex;
        });
        _loadTabContent(_tabs[newIndex].id);
      }
    }
  }

  Future<void> _loadTabContent(String tabId) async {
    try {
      final contentResponse = await _repository.getSystemContent(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
      );

      if (contentResponse.isSuccess && contentResponse.data != null) {
        final tabContent = contentResponse.data!
            .where((content) => content.metadata?['tabId'] == tabId)
            .toList();
        
        setState(() {
          _currentTabContent = tabContent;
        });
      }
    } catch (e) {
      print('Error loading tab content: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.systemName} Management'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_box),
            onPressed: () => _showAddTabDialog(),
            tooltip: 'Add New Tab',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSystemTabs,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(_error!, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSystemTabs,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_tabs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.tab, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No tabs available for ${widget.systemName}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddTabDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add First Tab'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Tab Bar
        Container(
          color: Colors.grey[100],
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Colors.blue[700],
            labelColor: Colors.blue[700],
            unselectedLabelColor: Colors.grey[600],
            tabs: _tabs.map((tab) => Tab(
              icon: Icon(_getIconData(tab.tabIcon)),
              text: tab.title,
            )).toList(),
          ),
        ),
        
        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _tabs.map((tab) => _buildTabContent(tab)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTabContent(SystemContent tab) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Tab actions
          Row(
            children: [
              Text(
                tab.title,
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _showEditTabDialog(tab),
                tooltip: 'Edit Tab',
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _showDeleteTabConfirmation(tab),
                tooltip: 'Delete Tab',
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Rich Text Editor
          Expanded(
            child: RichTextEditor(
              initialContent: tab.richContent ?? '',
              format: tab.contentFormat == 'html' ? ContentFormat.html : ContentFormat.markdown,
              onContentChanged: (content) => _saveTabContent(tab, content),
              placeholder: 'Enter content for ${tab.title} tab...',
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'water_drop': return Icons.water_drop;
      case 'analytics': return Icons.analytics;
      case 'build': return Icons.build;
      case 'contact_phone': return Icons.contact_phone;
      case 'electrical_services': return Icons.electrical_services;
      case 'power': return Icons.power;
      case 'security': return Icons.security;
      case 'videocam': return Icons.videocam;
      case 'key': return Icons.key;
      case 'report': return Icons.report;
      case 'settings': return Icons.settings;
      case 'dashboard': return Icons.dashboard;
      default: return Icons.tab;
    }
  }

  Future<void> _saveTabContent(SystemContent tab, String content) async {
    try {
      await _repository.updateSystemContent(
        propertyId: widget.propertyId,
        contentId: tab.id,
        data: {
          'richContent': content,
          'contentFormat': tab.contentFormat ?? 'markdown',
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving content: $e')),
      );
    }
  }

  void _showAddTabDialog() {
    showDialog(
      context: context,
      builder: (context) => TabEditDialog(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        onSaved: () {
          Navigator.of(context).pop();
          _loadSystemTabs();
        },
      ),
    );
  }

  void _showEditTabDialog(SystemContent tab) {
    showDialog(
      context: context,
      builder: (context) => TabEditDialog(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        tab: tab,
        onSaved: () {
          Navigator.of(context).pop();
          _loadSystemTabs();
        },
      ),
    );
  }

  void _showDeleteTabConfirmation(SystemContent tab) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tab'),
        content: Text('Are you sure you want to delete the "${tab.title}" tab? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteTab(tab);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTab(SystemContent tab) async {
    try {
      final response = await _repository.deleteSystemContent(
        propertyId: widget.propertyId,
        contentId: tab.id,
      );

      if (response.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tab deleted successfully')),
        );
        _loadSystemTabs();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete tab: ${response.message}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting tab: $e')),
      );
    }
  }
}

// Tab Edit Dialog
class TabEditDialog extends StatefulWidget {
  final String propertyId;
  final String systemType;
  final SystemContent? tab;
  final VoidCallback onSaved;

  const TabEditDialog({
    super.key,
    required this.propertyId,
    required this.systemType,
    this.tab,
    required this.onSaved,
  });

  @override
  State<TabEditDialog> createState() => _TabEditDialogState();
}

class _TabEditDialogState extends State<TabEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final PropertySystemRepository _repository = serviceLocator.propertySystemRepository;

  late TextEditingController _titleController;
  late String _selectedIcon;
  late String _selectedFormat;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'Dashboard', 'value': 'dashboard', 'icon': Icons.dashboard},
    {'name': 'Analytics', 'value': 'analytics', 'icon': Icons.analytics},
    {'name': 'Build', 'value': 'build', 'icon': Icons.build},
    {'name': 'Contact Phone', 'value': 'contact_phone', 'icon': Icons.contact_phone},
    {'name': 'Settings', 'value': 'settings', 'icon': Icons.settings},
    {'name': 'Security', 'value': 'security', 'icon': Icons.security},
    {'name': 'Water Drop', 'value': 'water_drop', 'icon': Icons.water_drop},
    {'name': 'Electrical Services', 'value': 'electrical_services', 'icon': Icons.electrical_services},
    {'name': 'Power', 'value': 'power', 'icon': Icons.power},
    {'name': 'Videocam', 'value': 'videocam', 'icon': Icons.videocam},
    {'name': 'Key', 'value': 'key', 'icon': Icons.key},
    {'name': 'Report', 'value': 'report', 'icon': Icons.report},
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.tab?.title ?? '');
    _selectedIcon = widget.tab?.tabIcon ?? 'dashboard';
    _selectedFormat = widget.tab?.contentFormat ?? 'markdown';
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.tab == null ? 'Add New Tab' : 'Edit Tab',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            Form(
              key: _formKey,
              child: Column(
                children: [
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Tab Title',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a tab title';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedIcon,
                    decoration: const InputDecoration(
                      labelText: 'Tab Icon',
                      border: OutlineInputBorder(),
                    ),
                    items: _availableIcons.map((iconData) {
                      return DropdownMenuItem<String>(
                        value: iconData['value'] as String,
                        child: Row(
                          children: [
                            Icon(iconData['icon'] as IconData),
                            const SizedBox(width: 8),
                            Text(iconData['name'] as String),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedIcon = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedFormat,
                    decoration: const InputDecoration(
                      labelText: 'Content Format',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'markdown',
                        child: Text('Markdown'),
                      ),
                      DropdownMenuItem(
                        value: 'html',
                        child: Text('HTML'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedFormat = value!;
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _saveTab,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(widget.tab == null ? 'Add' : 'Save'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveTab() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final tabData = {
        'systemType': widget.systemType,
        'contentType': 'tab_content',
        'title': _titleController.text,
        'content': {},
        'richContent': widget.tab?.richContent ?? '# ${_titleController.text}\n\nWelcome to the ${_titleController.text} tab.',
        'contentFormat': _selectedFormat,
        'isTab': true,
        'tabIcon': _selectedIcon,
        'displayOrder': widget.tab?.displayOrder ?? 1,
      };

      ApiResponse response;
      if (widget.tab == null) {
        response = await _repository.createSystemContent(
          propertyId: widget.propertyId,
          data: tabData,
        );
      } else {
        response = await _repository.updateSystemContent(
          propertyId: widget.propertyId,
          contentId: widget.tab!.id,
          data: tabData,
        );
      }

      if (response.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.tab == null
                ? 'Tab added successfully'
                : 'Tab updated successfully'),
          ),
        );
        widget.onSaved();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save tab: ${response.message}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving tab: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

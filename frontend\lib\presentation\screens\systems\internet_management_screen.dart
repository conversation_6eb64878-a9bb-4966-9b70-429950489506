import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'unified_system_management_screen.dart';

class InternetManagementScreen extends ConsumerWidget {
  final String propertyId;
  
  const InternetManagementScreen({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return UnifiedSystemManagementScreen(
      propertyId: propertyId,
      systemType: 'INTERNET',
      systemName: 'Internet Management',
      systemIcon: Icons.wifi,
      systemColor: Colors.purple,
    );
  }
}

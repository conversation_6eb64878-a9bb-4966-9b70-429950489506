import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/construction_sites_service.dart';
import '../../core/services/service_locator.dart';

// Service provider
final constructionSitesServiceProvider = Provider<ConstructionSitesService>((ref) {
  return getService<ConstructionSitesService>();
});

// Construction sites provider
final constructionSitesProvider = FutureProvider<List<ConstructionSite>>((ref) async {
  try {
    final service = ref.read(constructionSitesServiceProvider);
    final response = await service.getConstructionSites();

    if (response.success && response.data != null) {
      return response.data!;
    } else {
      // Return empty list for now if API is not implemented
      return [];
    }
  } catch (e) {
    // Return empty list for graceful degradation
    return [];
  }
});

// Construction site by ID provider
final constructionSiteByIdProvider = FutureProvider.family<ConstructionSite, String>((ref, siteId) async {
  final service = ref.read(constructionSitesServiceProvider);
  final response = await service.getConstructionSiteById(siteId);
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch construction site');
  }
});

// Construction site attendance provider
final constructionSiteAttendanceProvider = FutureProvider.family<PaginatedAttendance, AttendanceQuery>((ref, query) async {
  final service = ref.read(constructionSitesServiceProvider);
  final response = await service.getAttendance(
    siteId: query.siteId,
    startDate: query.startDate,
    endDate: query.endDate,
    page: query.page,
    limit: query.limit,
  );
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch attendance');
  }
});

// Construction site management state providers
final constructionSiteFormProvider = StateNotifierProvider<ConstructionSiteFormNotifier, ConstructionSiteFormState>((ref) {
  return ConstructionSiteFormNotifier(ref.read(constructionSitesServiceProvider));
});

class ConstructionSiteFormState {
  final bool isLoading;
  final String? error;
  final String? successMessage;
  final ConstructionSite? site;

  ConstructionSiteFormState({
    this.isLoading = false,
    this.error,
    this.successMessage,
    this.site,
  });

  ConstructionSiteFormState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
    ConstructionSite? site,
  }) {
    return ConstructionSiteFormState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
      site: site ?? this.site,
    );
  }
}

class ConstructionSiteFormNotifier extends StateNotifier<ConstructionSiteFormState> {
  final ConstructionSitesService _service;

  ConstructionSiteFormNotifier(this._service) : super(ConstructionSiteFormState());

  Future<void> createConstructionSite({
    required String propertyId,
    required String name,
    required String location,
    required int workers,
    required String status,
    required double progress,
    String? description,
    String? startDate,
    String? expectedEndDate,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.createConstructionSite(
        propertyId: propertyId,
        name: name,
        location: location,
        workers: workers,
        status: status,
        progress: progress,
        description: description,
        startDate: startDate,
        expectedEndDate: expectedEndDate,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Construction site created successfully',
          site: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to create construction site',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create construction site: $e',
      );
    }
  }

  Future<void> updateConstructionSite({
    required String siteId,
    String? name,
    String? location,
    int? workers,
    String? status,
    double? progress,
    String? description,
    String? startDate,
    String? expectedEndDate,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.updateConstructionSite(
        siteId: siteId,
        name: name,
        location: location,
        workers: workers,
        status: status,
        progress: progress,
        description: description,
        startDate: startDate,
        expectedEndDate: expectedEndDate,
      );

      if (response.success && response.data != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Construction site updated successfully',
          site: response.data!,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to update construction site',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update construction site: $e',
      );
    }
  }

  Future<void> deleteConstructionSite(String siteId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.deleteConstructionSite(siteId);

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Construction site deleted successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to delete construction site',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete construction site: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Attendance management provider
final attendanceFormProvider = StateNotifierProvider<AttendanceFormNotifier, AttendanceFormState>((ref) {
  return AttendanceFormNotifier(ref.read(constructionSitesServiceProvider));
});

class AttendanceFormState {
  final bool isLoading;
  final String? error;
  final String? successMessage;

  AttendanceFormState({
    this.isLoading = false,
    this.error,
    this.successMessage,
  });

  AttendanceFormState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
  }) {
    return AttendanceFormState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
    );
  }
}

class AttendanceFormNotifier extends StateNotifier<AttendanceFormState> {
  final ConstructionSitesService _service;

  AttendanceFormNotifier(this._service) : super(AttendanceFormState());

  Future<void> recordAttendance({
    required String siteId,
    required String date,
    required int presentWorkers,
    String? notes,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _service.recordAttendance(
        siteId: siteId,
        date: date,
        presentWorkers: presentWorkers,
        notes: notes,
      );

      if (response.success) {
        state = state.copyWith(
          isLoading: false,
          successMessage: response.message ?? 'Attendance recorded successfully',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? 'Failed to record attendance',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to record attendance: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

// Query classes
class AttendanceQuery {
  final String siteId;
  final String? startDate;
  final String? endDate;
  final int page;
  final int limit;

  AttendanceQuery({
    required this.siteId,
    this.startDate,
    this.endDate,
    this.page = 1,
    this.limit = 30,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AttendanceQuery &&
          runtimeType == other.runtimeType &&
          siteId == other.siteId &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          page == other.page &&
          limit == other.limit;

  @override
  int get hashCode =>
      siteId.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      page.hashCode ^
      limit.hashCode;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmployeeDepartment _$EmployeeDepartmentFromJson(Map<String, dynamic> json) =>
    EmployeeDepartment(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      employeeCount: (json['employeeCount'] as num).toInt(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$EmployeeDepartmentToJson(EmployeeDepartment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'description': instance.description,
      'isActive': instance.isActive,
      'employeeCount': instance.employeeCount,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

EmployeeStatus _$EmployeeStatusFromJson(Map<String, dynamic> json) =>
    EmployeeStatus(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      employeeCount: (json['employeeCount'] as num).toInt(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$EmployeeStatusToJson(EmployeeStatus instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'description': instance.description,
      'isActive': instance.isActive,
      'employeeCount': instance.employeeCount,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

EmployeeRole _$EmployeeRoleFromJson(Map<String, dynamic> json) => EmployeeRole(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String?,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isActive: json['isActive'] as bool,
      employeeCount: (json['employeeCount'] as num).toInt(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$EmployeeRoleToJson(EmployeeRole instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'description': instance.description,
      'permissions': instance.permissions,
      'isActive': instance.isActive,
      'employeeCount': instance.employeeCount,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

EmployeeShift _$EmployeeShiftFromJson(Map<String, dynamic> json) =>
    EmployeeShift(
      id: json['id'] as String,
      name: json['name'] as String,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      durationHours: (json['durationHours'] as num).toInt(),
      workDays:
          (json['workDays'] as List<dynamic>).map((e) => e as String).toList(),
      isActive: json['isActive'] as bool,
      employeeCount: (json['employeeCount'] as num).toInt(),
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$EmployeeShiftToJson(EmployeeShift instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'durationHours': instance.durationHours,
      'workDays': instance.workDays,
      'isActive': instance.isActive,
      'employeeCount': instance.employeeCount,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

EmployeeConfigSummary _$EmployeeConfigSummaryFromJson(
        Map<String, dynamic> json) =>
    EmployeeConfigSummary(
      totalDepartments: (json['totalDepartments'] as num).toInt(),
      activeDepartments: (json['activeDepartments'] as num).toInt(),
      totalStatuses: (json['totalStatuses'] as num).toInt(),
      activeStatuses: (json['activeStatuses'] as num).toInt(),
      totalRoles: (json['totalRoles'] as num).toInt(),
      activeRoles: (json['activeRoles'] as num).toInt(),
      totalShifts: (json['totalShifts'] as num).toInt(),
      activeShifts: (json['activeShifts'] as num).toInt(),
      totalEmployees: (json['totalEmployees'] as num).toInt(),
      activeEmployees: (json['activeEmployees'] as num).toInt(),
    );

Map<String, dynamic> _$EmployeeConfigSummaryToJson(
        EmployeeConfigSummary instance) =>
    <String, dynamic>{
      'totalDepartments': instance.totalDepartments,
      'activeDepartments': instance.activeDepartments,
      'totalStatuses': instance.totalStatuses,
      'activeStatuses': instance.activeStatuses,
      'totalRoles': instance.totalRoles,
      'activeRoles': instance.activeRoles,
      'totalShifts': instance.totalShifts,
      'activeShifts': instance.activeShifts,
      'totalEmployees': instance.totalEmployees,
      'activeEmployees': instance.activeEmployees,
    };

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/user.dart';
import '../../providers/auth_providers.dart';
import '../../routes/app_router.dart';

class MainNavigationScreen extends ConsumerStatefulWidget {
  final Widget child;

  const MainNavigationScreen({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends ConsumerState<MainNavigationScreen> {
  int _selectedIndex = 0;

  List<NavigationItem> _getNavigationItems(BuildContext context, WidgetRef ref) {
    final userAsyncValue = ref.watch(currentUserProvider);
    final user = userAsyncValue.maybeWhen(
      data: (user) => user,
      orElse: () => null,
    );

    final baseItems = [
      NavigationItem(
        icon: Icons.dashboard,
        label: 'Dashboard',
        route: AppRoutes.dashboard,
      ),
      NavigationItem(
        icon: Icons.business,
        label: 'Properties',
        route: AppRoutes.properties,
      ),
      NavigationItem(
        icon: Icons.work,
        label: 'Office',
        route: AppRoutes.officeManagement,
      ),
      NavigationItem(
        icon: Icons.settings,
        label: 'Settings',
        route: AppRoutes.settings,
      ),
    ];

    // Add admin panel for super admins and admins
    if (user?.role == 'SUPER_ADMIN' || user?.role == 'ADMIN') {
      baseItems.insert(baseItems.length - 1, NavigationItem(
        icon: Icons.admin_panel_settings,
        label: 'Admin',
        route: AppRoutes.adminPanel,
      ));
    }

    return baseItems;
  }

  void _onItemTapped(int index) {
    final navigationItems = _getNavigationItems(context, ref);
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(navigationItems[index].route);
    }
  }

  @override
  Widget build(BuildContext context) {
    final navigationItems = _getNavigationItems(context, ref);

    // Update selected index based on current route (only if GoRouter is available)
    try {
      final currentLocation = GoRouterState.of(context).uri.toString();
      _updateSelectedIndex(currentLocation, navigationItems);
    } catch (e) {
      // GoRouter not available (e.g., in tests), use default index
    }

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        backgroundColor: Colors.white,
        elevation: 8,
        items: navigationItems.map((item) {
          return BottomNavigationBarItem(
            icon: Icon(item.icon),
            label: item.label,
            tooltip: item.label,
          );
        }).toList(),
      ),
    );
  }

  void _updateSelectedIndex(String location, List<NavigationItem> navigationItems) {
    for (int i = 0; i < navigationItems.length; i++) {
      if (location.startsWith(navigationItems[i].route)) {
        if (_selectedIndex != i) {
          setState(() {
            _selectedIndex = i;
          });
        }
        break;
      }
    }
  }
}

class NavigationItem {
  final IconData icon;
  final String label;
  final String route;

  const NavigationItem({
    required this.icon,
    required this.label,
    required this.route,
  });
}

// Custom App Bar for consistent styling across screens
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final PreferredSizeWidget? bottom;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: foregroundColor ?? Colors.white,
        ),
      ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation,
      actions: actions,
      leading: leading,
      bottom: bottom,
      systemOverlayStyle: Theme.of(context).appBarTheme.systemOverlayStyle,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
  );
}

// Status indicator widget for system health
class StatusIndicator extends StatelessWidget {
  final String status;
  final double size;
  final bool showLabel;

  const StatusIndicator({
    super.key,
    required this.status,
    this.size = 12.0,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    Color color;
    String label;

    switch (status.toLowerCase()) {
      case AppConstants.operationalStatus:
        color = Theme.of(context).extension<CustomColors>()?.operationalColor ?? Colors.green;
        label = 'Operational';
        break;
      case AppConstants.warningStatus:
        color = Theme.of(context).extension<CustomColors>()?.warningColor ?? Colors.orange;
        label = 'Warning';
        break;
      case AppConstants.criticalStatus:
        color = Theme.of(context).extension<CustomColors>()?.criticalColor ?? Colors.red;
        label = 'Critical';
        break;
      case AppConstants.offlineStatus:
        color = Theme.of(context).extension<CustomColors>()?.offlineColor ?? Colors.grey;
        label = 'Offline';
        break;
      default:
        color = Colors.grey;
        label = 'Unknown';
    }

    if (showLabel) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }
}



// Loading widget for consistent loading states
class LoadingWidget extends StatelessWidget {
  final String? message;
  final double size;

  const LoadingWidget({
    super.key,
    this.message,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

// Error widget for consistent error states
class ErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData icon;

  const ErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon = Icons.error_outline,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

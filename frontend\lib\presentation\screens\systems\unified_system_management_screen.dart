import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/system.dart';
import '../../providers/property_system_providers.dart';
import '../admin/enhanced_system_content_management_screen.dart';
import '../main/main_navigation_screen.dart';

class UnifiedSystemManagementScreen extends ConsumerStatefulWidget {
  final String propertyId;
  final String systemType;
  final String systemName;
  final IconData systemIcon;
  final Color systemColor;
  
  const UnifiedSystemManagementScreen({
    super.key,
    required this.propertyId,
    required this.systemType,
    required this.systemName,
    required this.systemIcon,
    required this.systemColor,
  });

  @override
  ConsumerState<UnifiedSystemManagementScreen> createState() => _UnifiedSystemManagementScreenState();
}

class _UnifiedSystemManagementScreenState extends ConsumerState<UnifiedSystemManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<SystemContent> _systemTabs = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSystemTabs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemTabs() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final contentProvider = systemContentProvider(SystemContentParams(
        propertyId: widget.propertyId,
        systemType: widget.systemType,
        contentType: 'tab_content',
      ));

      final contentList = await ref.read(contentProvider.future);
      final tabs = contentList.where((content) => content.isTab == true).toList();
      
      // Sort tabs by display order
      tabs.sort((a, b) => (a.displayOrder ?? 0).compareTo(b.displayOrder ?? 0));

      if (tabs.isEmpty) {
        await _createDefaultTabs();
        final updatedContentList = await ref.refresh(contentProvider.future);
        final updatedTabs = updatedContentList.where((content) => content.isTab == true).toList();
        updatedTabs.sort((a, b) => (a.displayOrder ?? 0).compareTo(b.displayOrder ?? 0));
        tabs.addAll(updatedTabs);
      }

      setState(() {
        _systemTabs = tabs;
        _isLoading = false;
      });

      // Initialize tab controller with the correct number of tabs
      _tabController = TabController(length: _systemTabs.length, vsync: this);
    } catch (e) {
      setState(() {
        _error = 'Error loading system tabs: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _createDefaultTabs() async {
    // Default tabs will be created by the seeded data
    // This is a placeholder for future API integration
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: CustomAppBar(
          title: widget.systemName,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: CustomAppBar(
          title: widget.systemName,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(_error!),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSystemTabs,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: CustomAppBar(
        title: widget.systemName,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _navigateToContentManagement,
            tooltip: 'Manage Content',
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with icon and title
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: widget.systemColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    widget.systemIcon,
                    color: widget.systemColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  widget.systemName,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Tab Bar
          if (_systemTabs.isNotEmpty)
            Container(
              color: Colors.grey[100],
              child: TabBar(
                controller: _tabController,
                indicatorColor: AppTheme.primaryColor,
                labelColor: AppTheme.primaryColor,
                unselectedLabelColor: Colors.grey[600],
                isScrollable: true,
                tabs: _systemTabs.map((tab) => Tab(text: tab.title)).toList(),
              ),
            ),
          
          // Tab Bar View
          Expanded(
            child: _systemTabs.isEmpty
                ? _buildEmptyState()
                : TabBarView(
                    controller: _tabController,
                    children: _systemTabs.map((tab) => _buildTabContent(tab.title)).toList(),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent(String tabTitle) {
    return Consumer(
      builder: (context, ref, child) {
        final contentProvider = systemContentProvider(SystemContentParams(
          propertyId: widget.propertyId,
          systemType: widget.systemType,
          contentType: 'tab_content',
        ));

        return ref.watch(contentProvider).when(
          data: (contentList) {
            // Find the content for this specific tab
            final tabContent = contentList.firstWhere(
              (content) => content.title == tabTitle,
              orElse: () => SystemContent(
                id: '',
                propertyId: widget.propertyId,
                systemType: widget.systemType,
                contentType: 'tab_content',
                title: tabTitle,
                content: {},
                richContent: 'No content available for $tabTitle tab.\n\nClick "Manage Content" to add content.',
                isActive: true,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            return _buildMarkdownContent(tabContent.richContent ?? 'No content available.');
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error loading content: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(contentProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMarkdownContent(String content) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: MarkdownBody(
        data: content,
        styleSheet: MarkdownStyleSheet(
          h1: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          h2: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          h3: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          p: const TextStyle(fontSize: 16, height: 1.5),
          listBullet: const TextStyle(fontSize: 16),
        ),
        onTapLink: (text, href, title) {
          if (href != null) {
            launchUrl(Uri.parse(href));
          }
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(widget.systemIcon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No ${widget.systemName.toLowerCase()} content available',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _navigateToContentManagement,
            icon: const Icon(Icons.add),
            label: const Text('Add Content'),
          ),
        ],
      ),
    );
  }

  void _refreshData() {
    // Refresh the content provider
    ref.invalidate(systemContentProvider(SystemContentParams(
      propertyId: widget.propertyId,
      systemType: widget.systemType,
      contentType: 'tab_content',
    )));
    
    // Reload tabs
    _loadSystemTabs();
  }

  void _navigateToContentManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EnhancedSystemContentManagementScreen(
          propertyId: widget.propertyId,
          systemType: widget.systemType,
          systemName: widget.systemName,
        ),
      ),
    ).then((_) {
      // Refresh the content when returning from content management
      _loadSystemTabs();
    });
  }
}

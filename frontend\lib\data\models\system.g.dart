// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'system.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SystemStatus _$SystemStatusFromJson(Map<String, dynamic> json) => SystemStatus(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemType: json['systemType'] as String,
      status: json['status'] as String,
      healthScore: (json['healthScore'] as num).toInt(),
      description: json['description'] as String?,
      lastChecked: json['lastChecked'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SystemStatusToJson(SystemStatus instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemType': instance.systemType,
      'status': instance.status,
      'healthScore': instance.healthScore,
      'description': instance.description,
      'lastChecked': instance.lastChecked,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'property': instance.property,
    };

WaterSystem _$WaterSystemFromJson(Map<String, dynamic> json) => WaterSystem(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      tankName: json['tankName'] as String,
      capacity: (json['capacity'] as num).toDouble(),
      currentLevel: (json['currentLevel'] as num).toDouble(),
      levelPercentage: (json['levelPercentage'] as num).toDouble(),
      pumpStatus: json['pumpStatus'] as String,
      flowRate: (json['flowRate'] as num?)?.toDouble(),
      pressure: (json['pressure'] as num?)?.toDouble(),
      quality: json['quality'] as String?,
      lastMaintenance: json['lastMaintenance'] as String?,
      nextMaintenance: json['nextMaintenance'] as String?,
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WaterSystemToJson(WaterSystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'tankName': instance.tankName,
      'capacity': instance.capacity,
      'currentLevel': instance.currentLevel,
      'levelPercentage': instance.levelPercentage,
      'pumpStatus': instance.pumpStatus,
      'flowRate': instance.flowRate,
      'pressure': instance.pressure,
      'quality': instance.quality,
      'lastMaintenance': instance.lastMaintenance,
      'nextMaintenance': instance.nextMaintenance,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'property': instance.property,
    };

ElectricitySystem _$ElectricitySystemFromJson(Map<String, dynamic> json) =>
    ElectricitySystem(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemName: json['systemName'] as String,
      generatorStatus: json['generatorStatus'] as String,
      fuelLevel: (json['fuelLevel'] as num?)?.toDouble(),
      powerConsumption: (json['powerConsumption'] as num?)?.toDouble(),
      voltage: (json['voltage'] as num?)?.toDouble(),
      frequency: (json['frequency'] as num?)?.toDouble(),
      loadPercentage: (json['loadPercentage'] as num?)?.toDouble(),
      mainsPowerStatus: json['mainsPowerStatus'] as String,
      batteryBackup: (json['batteryBackup'] as num?)?.toDouble(),
      lastMaintenance: json['lastMaintenance'] as String?,
      nextMaintenance: json['nextMaintenance'] as String?,
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ElectricitySystemToJson(ElectricitySystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemName': instance.systemName,
      'generatorStatus': instance.generatorStatus,
      'fuelLevel': instance.fuelLevel,
      'powerConsumption': instance.powerConsumption,
      'voltage': instance.voltage,
      'frequency': instance.frequency,
      'loadPercentage': instance.loadPercentage,
      'mainsPowerStatus': instance.mainsPowerStatus,
      'batteryBackup': instance.batteryBackup,
      'lastMaintenance': instance.lastMaintenance,
      'nextMaintenance': instance.nextMaintenance,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'property': instance.property,
    };

SecuritySystem _$SecuritySystemFromJson(Map<String, dynamic> json) =>
    SecuritySystem(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemName: json['systemName'] as String,
      cameraCount: (json['cameraCount'] as num).toInt(),
      activeCameras: (json['activeCameras'] as num).toInt(),
      accessPoints: (json['accessPoints'] as num).toInt(),
      activeAccess: (json['activeAccess'] as num).toInt(),
      alarmStatus: json['alarmStatus'] as String,
      motionDetected: json['motionDetected'] as bool,
      lastIncident: json['lastIncident'] as String?,
      lastMaintenance: json['lastMaintenance'] as String?,
      nextMaintenance: json['nextMaintenance'] as String?,
      isActive: json['isActive'] as bool,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
      property: json['property'] == null
          ? null
          : PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
      cameras: (json['cameras'] as List<dynamic>)
          .map((e) => SecurityCamera.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SecuritySystemToJson(SecuritySystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemName': instance.systemName,
      'cameraCount': instance.cameraCount,
      'activeCameras': instance.activeCameras,
      'accessPoints': instance.accessPoints,
      'activeAccess': instance.activeAccess,
      'alarmStatus': instance.alarmStatus,
      'motionDetected': instance.motionDetected,
      'lastIncident': instance.lastIncident,
      'lastMaintenance': instance.lastMaintenance,
      'nextMaintenance': instance.nextMaintenance,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'property': instance.property,
      'cameras': instance.cameras,
    };

SecurityCamera _$SecurityCameraFromJson(Map<String, dynamic> json) =>
    SecurityCamera(
      id: json['id'] as String,
      securitySystemId: json['securitySystemId'] as String,
      cameraName: json['cameraName'] as String,
      location: json['location'] as String,
      status: json['status'] as String,
      ipAddress: json['ipAddress'] as String?,
      recordingStatus: json['recordingStatus'] as String,
      lastPing: json['lastPing'] as String?,
      createdAt: json['createdAt'] as String,
      updatedAt: json['updatedAt'] as String,
    );

Map<String, dynamic> _$SecurityCameraToJson(SecurityCamera instance) =>
    <String, dynamic>{
      'id': instance.id,
      'securitySystemId': instance.securitySystemId,
      'cameraName': instance.cameraName,
      'location': instance.location,
      'status': instance.status,
      'ipAddress': instance.ipAddress,
      'recordingStatus': instance.recordingStatus,
      'lastPing': instance.lastPing,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
    };

WaterSystemsResponse _$WaterSystemsResponseFromJson(
        Map<String, dynamic> json) =>
    WaterSystemsResponse(
      systems: (json['systems'] as List<dynamic>)
          .map((e) => WaterSystem.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary:
          WaterSystemsSummary.fromJson(json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WaterSystemsResponseToJson(
        WaterSystemsResponse instance) =>
    <String, dynamic>{
      'systems': instance.systems,
      'summary': instance.summary,
    };

WaterSystemsSummary _$WaterSystemsSummaryFromJson(Map<String, dynamic> json) =>
    WaterSystemsSummary(
      totalSystems: (json['totalSystems'] as num).toInt(),
      totalCapacity: (json['totalCapacity'] as num).toDouble(),
      totalCurrentLevel: (json['totalCurrentLevel'] as num).toDouble(),
      averageLevel: (json['averageLevel'] as num).toInt(),
      activePumps: (json['activePumps'] as num).toInt(),
      maintenanceRequired: (json['maintenanceRequired'] as num).toInt(),
    );

Map<String, dynamic> _$WaterSystemsSummaryToJson(
        WaterSystemsSummary instance) =>
    <String, dynamic>{
      'totalSystems': instance.totalSystems,
      'totalCapacity': instance.totalCapacity,
      'totalCurrentLevel': instance.totalCurrentLevel,
      'averageLevel': instance.averageLevel,
      'activePumps': instance.activePumps,
      'maintenanceRequired': instance.maintenanceRequired,
    };

ElectricitySystemsResponse _$ElectricitySystemsResponseFromJson(
        Map<String, dynamic> json) =>
    ElectricitySystemsResponse(
      systems: (json['systems'] as List<dynamic>)
          .map((e) => ElectricitySystem.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary: ElectricitySystemsSummary.fromJson(
          json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ElectricitySystemsResponseToJson(
        ElectricitySystemsResponse instance) =>
    <String, dynamic>{
      'systems': instance.systems,
      'summary': instance.summary,
    };

ElectricitySystemsSummary _$ElectricitySystemsSummaryFromJson(
        Map<String, dynamic> json) =>
    ElectricitySystemsSummary(
      totalSystems: (json['totalSystems'] as num).toInt(),
      totalPowerConsumption: (json['totalPowerConsumption'] as num).toDouble(),
      averageFuelLevel: (json['averageFuelLevel'] as num).toInt(),
      activeGenerators: (json['activeGenerators'] as num).toInt(),
      mainsAvailable: (json['mainsAvailable'] as num).toInt(),
      maintenanceRequired: (json['maintenanceRequired'] as num).toInt(),
    );

Map<String, dynamic> _$ElectricitySystemsSummaryToJson(
        ElectricitySystemsSummary instance) =>
    <String, dynamic>{
      'totalSystems': instance.totalSystems,
      'totalPowerConsumption': instance.totalPowerConsumption,
      'averageFuelLevel': instance.averageFuelLevel,
      'activeGenerators': instance.activeGenerators,
      'mainsAvailable': instance.mainsAvailable,
      'maintenanceRequired': instance.maintenanceRequired,
    };

SecuritySystemsResponse _$SecuritySystemsResponseFromJson(
        Map<String, dynamic> json) =>
    SecuritySystemsResponse(
      systems: (json['systems'] as List<dynamic>)
          .map((e) => SecuritySystem.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary: SecuritySystemsSummary.fromJson(
          json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SecuritySystemsResponseToJson(
        SecuritySystemsResponse instance) =>
    <String, dynamic>{
      'systems': instance.systems,
      'summary': instance.summary,
    };

SecuritySystemsSummary _$SecuritySystemsSummaryFromJson(
        Map<String, dynamic> json) =>
    SecuritySystemsSummary(
      totalSystems: (json['totalSystems'] as num).toInt(),
      totalCameras: (json['totalCameras'] as num).toInt(),
      totalActiveCameras: (json['totalActiveCameras'] as num).toInt(),
      totalAccessPoints: (json['totalAccessPoints'] as num).toInt(),
      totalActiveAccess: (json['totalActiveAccess'] as num).toInt(),
      armedSystems: (json['armedSystems'] as num).toInt(),
      triggeredAlarms: (json['triggeredAlarms'] as num).toInt(),
      maintenanceRequired: (json['maintenanceRequired'] as num).toInt(),
      cameraOperationalRate: (json['cameraOperationalRate'] as num).toInt(),
      accessOperationalRate: (json['accessOperationalRate'] as num).toInt(),
    );

Map<String, dynamic> _$SecuritySystemsSummaryToJson(
        SecuritySystemsSummary instance) =>
    <String, dynamic>{
      'totalSystems': instance.totalSystems,
      'totalCameras': instance.totalCameras,
      'totalActiveCameras': instance.totalActiveCameras,
      'totalAccessPoints': instance.totalAccessPoints,
      'totalActiveAccess': instance.totalActiveAccess,
      'armedSystems': instance.armedSystems,
      'triggeredAlarms': instance.triggeredAlarms,
      'maintenanceRequired': instance.maintenanceRequired,
      'cameraOperationalRate': instance.cameraOperationalRate,
      'accessOperationalRate': instance.accessOperationalRate,
    };

PropertyInfo _$PropertyInfoFromJson(Map<String, dynamic> json) => PropertyInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String?,
      address: json['address'] as String?,
    );

Map<String, dynamic> _$PropertyInfoToJson(PropertyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'address': instance.address,
    };

WaterConsumption _$WaterConsumptionFromJson(Map<String, dynamic> json) =>
    WaterConsumption(
      todayUsage: (json['todayUsage'] as num).toDouble(),
      weekUsage: (json['weekUsage'] as num).toDouble(),
      monthUsage: (json['monthUsage'] as num).toDouble(),
      averageDailyUsage: (json['averageDailyUsage'] as num).toDouble(),
      todayUsageChange: (json['todayUsageChange'] as num).toDouble(),
      weekUsageChange: (json['weekUsageChange'] as num).toDouble(),
      monthUsageChange: (json['monthUsageChange'] as num).toDouble(),
      dailyUsage: (json['dailyUsage'] as List<dynamic>)
          .map((e) => DailyUsage.fromJson(e as Map<String, dynamic>))
          .toList(),
      usageByTank: (json['usageByTank'] as List<dynamic>)
          .map((e) => TankUsage.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WaterConsumptionToJson(WaterConsumption instance) =>
    <String, dynamic>{
      'todayUsage': instance.todayUsage,
      'weekUsage': instance.weekUsage,
      'monthUsage': instance.monthUsage,
      'averageDailyUsage': instance.averageDailyUsage,
      'todayUsageChange': instance.todayUsageChange,
      'weekUsageChange': instance.weekUsageChange,
      'monthUsageChange': instance.monthUsageChange,
      'dailyUsage': instance.dailyUsage,
      'usageByTank': instance.usageByTank,
    };

DailyUsage _$DailyUsageFromJson(Map<String, dynamic> json) => DailyUsage(
      date: json['date'] as String,
      usage: (json['usage'] as num).toDouble(),
    );

Map<String, dynamic> _$DailyUsageToJson(DailyUsage instance) =>
    <String, dynamic>{
      'date': instance.date,
      'usage': instance.usage,
    };

TankUsage _$TankUsageFromJson(Map<String, dynamic> json) => TankUsage(
      tankName: json['tankName'] as String,
      usage: (json['usage'] as num).toDouble(),
      usagePercentage: (json['usagePercentage'] as num).toDouble(),
    );

Map<String, dynamic> _$TankUsageToJson(TankUsage instance) => <String, dynamic>{
      'tankName': instance.tankName,
      'usage': instance.usage,
      'usagePercentage': instance.usagePercentage,
    };

WaterMaintenance _$WaterMaintenanceFromJson(Map<String, dynamic> json) =>
    WaterMaintenance(
      overdueTasks: (json['overdueTasks'] as num).toInt(),
      dueSoonTasks: (json['dueSoonTasks'] as num).toInt(),
      completedTasks: (json['completedTasks'] as num).toInt(),
      upcomingMaintenance: (json['upcomingMaintenance'] as List<dynamic>)
          .map((e) => MaintenanceTask.fromJson(e as Map<String, dynamic>))
          .toList(),
      maintenanceHistory: (json['maintenanceHistory'] as List<dynamic>)
          .map((e) => MaintenanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
      tasks: (json['tasks'] as List<dynamic>)
          .map((e) => MaintenanceTask.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WaterMaintenanceToJson(WaterMaintenance instance) =>
    <String, dynamic>{
      'overdueTasks': instance.overdueTasks,
      'dueSoonTasks': instance.dueSoonTasks,
      'completedTasks': instance.completedTasks,
      'upcomingMaintenance': instance.upcomingMaintenance,
      'maintenanceHistory': instance.maintenanceHistory,
      'tasks': instance.tasks,
    };

MaintenanceTask _$MaintenanceTaskFromJson(Map<String, dynamic> json) =>
    MaintenanceTask(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      dueDate: DateTime.parse(json['dueDate'] as String),
      priority: json['priority'] as String,
      status: json['status'] as String,
      frequency: json['frequency'] as String?,
      lastCompleted: json['lastCompleted'] == null
          ? null
          : DateTime.parse(json['lastCompleted'] as String),
    );

Map<String, dynamic> _$MaintenanceTaskToJson(MaintenanceTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'dueDate': instance.dueDate.toIso8601String(),
      'priority': instance.priority,
      'status': instance.status,
      'frequency': instance.frequency,
      'lastCompleted': instance.lastCompleted?.toIso8601String(),
    };

MaintenanceRecord _$MaintenanceRecordFromJson(Map<String, dynamic> json) =>
    MaintenanceRecord(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      completedDate: DateTime.parse(json['completedDate'] as String),
      cost: (json['cost'] as num?)?.toDouble(),
      technician: json['technician'] as String,
      notes: json['notes'] as String,
    );

Map<String, dynamic> _$MaintenanceRecordToJson(MaintenanceRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'type': instance.type,
      'completedDate': instance.completedDate.toIso8601String(),
      'cost': instance.cost,
      'technician': instance.technician,
      'notes': instance.notes,
    };

WaterQuality _$WaterQualityFromJson(Map<String, dynamic> json) => WaterQuality(
      overallRating: json['overallRating'] as String,
      lastTested: DateTime.parse(json['lastTested'] as String),
      alerts:
          (json['alerts'] as List<dynamic>).map((e) => e as String).toList(),
      parameters: (json['parameters'] as List<dynamic>)
          .map((e) => QualityParameter.fromJson(e as Map<String, dynamic>))
          .toList(),
      history: (json['history'] as List<dynamic>)
          .map((e) => QualityTest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WaterQualityToJson(WaterQuality instance) =>
    <String, dynamic>{
      'overallRating': instance.overallRating,
      'lastTested': instance.lastTested.toIso8601String(),
      'alerts': instance.alerts,
      'parameters': instance.parameters,
      'history': instance.history,
    };

QualityParameter _$QualityParameterFromJson(Map<String, dynamic> json) =>
    QualityParameter(
      name: json['name'] as String,
      value: (json['value'] as num).toDouble(),
      minValue: (json['minValue'] as num).toDouble(),
      maxValue: (json['maxValue'] as num).toDouble(),
      unit: json['unit'] as String,
    );

Map<String, dynamic> _$QualityParameterToJson(QualityParameter instance) =>
    <String, dynamic>{
      'name': instance.name,
      'value': instance.value,
      'minValue': instance.minValue,
      'maxValue': instance.maxValue,
      'unit': instance.unit,
    };

QualityTest _$QualityTestFromJson(Map<String, dynamic> json) => QualityTest(
      id: json['id'] as String,
      testDate: DateTime.parse(json['testDate'] as String),
      result: json['result'] as String,
      notes: json['notes'] as String,
      technician: json['technician'] as String,
    );

Map<String, dynamic> _$QualityTestToJson(QualityTest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'testDate': instance.testDate.toIso8601String(),
      'result': instance.result,
      'notes': instance.notes,
      'technician': instance.technician,
    };

WaterContact _$WaterContactFromJson(Map<String, dynamic> json) => WaterContact(
      id: json['id'] as String,
      name: json['name'] as String,
      organization: json['organization'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$WaterContactToJson(WaterContact instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'organization': instance.organization,
      'phone': instance.phone,
      'email': instance.email,
      'type': instance.type,
    };

PropertySystemConfig _$PropertySystemConfigFromJson(
        Map<String, dynamic> json) =>
    PropertySystemConfig(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemType: json['systemType'] as String,
      isEnabled: json['isEnabled'] as bool,
      displayName: json['displayName'] as String?,
      displayOrder: (json['displayOrder'] as num?)?.toInt(),
      configuration: json['configuration'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PropertySystemConfigToJson(
        PropertySystemConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemType': instance.systemType,
      'isEnabled': instance.isEnabled,
      'displayName': instance.displayName,
      'displayOrder': instance.displayOrder,
      'configuration': instance.configuration,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

SystemContent _$SystemContentFromJson(Map<String, dynamic> json) =>
    SystemContent(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemType: json['systemType'] as String,
      contentType: json['contentType'] as String,
      title: json['title'] as String,
      content: json['content'] as Map<String, dynamic>,
      richContent: json['richContent'] as String?,
      contentFormat: json['contentFormat'] as String?,
      displayOrder: (json['displayOrder'] as num?)?.toInt(),
      isActive: json['isActive'] as bool,
      isTab: json['isTab'] as bool? ?? false,
      tabIcon: json['tabIcon'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SystemContentToJson(SystemContent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemType': instance.systemType,
      'contentType': instance.contentType,
      'title': instance.title,
      'content': instance.content,
      'richContent': instance.richContent,
      'contentFormat': instance.contentFormat,
      'displayOrder': instance.displayOrder,
      'isActive': instance.isActive,
      'isTab': instance.isTab,
      'tabIcon': instance.tabIcon,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

SystemContact _$SystemContactFromJson(Map<String, dynamic> json) =>
    SystemContact(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      systemType: json['systemType'] as String,
      name: json['name'] as String,
      organization: json['organization'] as String?,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      contactType: json['contactType'] as String,
      isActive: json['isActive'] as bool,
      displayOrder: (json['displayOrder'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SystemContactToJson(SystemContact instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'systemType': instance.systemType,
      'name': instance.name,
      'organization': instance.organization,
      'phone': instance.phone,
      'email': instance.email,
      'contactType': instance.contactType,
      'isActive': instance.isActive,
      'displayOrder': instance.displayOrder,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

AccessControl _$AccessControlFromJson(Map<String, dynamic> json) =>
    AccessControl(
      totalAccessPoints: (json['totalAccessPoints'] as num).toInt(),
      activeAccessPoints: (json['activeAccessPoints'] as num).toInt(),
      todayEvents: (json['todayEvents'] as num).toInt(),
      recentEvents: (json['recentEvents'] as List<dynamic>)
          .map((e) => AccessEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      accessPoints: (json['accessPoints'] as List<dynamic>)
          .map((e) => AccessPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AccessControlToJson(AccessControl instance) =>
    <String, dynamic>{
      'totalAccessPoints': instance.totalAccessPoints,
      'activeAccessPoints': instance.activeAccessPoints,
      'todayEvents': instance.todayEvents,
      'recentEvents': instance.recentEvents,
      'accessPoints': instance.accessPoints,
    };

AccessEvent _$AccessEventFromJson(Map<String, dynamic> json) => AccessEvent(
      id: json['id'] as String,
      eventType: json['eventType'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String?,
      userName: json['userName'] as String?,
    );

Map<String, dynamic> _$AccessEventToJson(AccessEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'eventType': instance.eventType,
      'description': instance.description,
      'location': instance.location,
      'timestamp': instance.timestamp.toIso8601String(),
      'userId': instance.userId,
      'userName': instance.userName,
    };

AccessPoint _$AccessPointFromJson(Map<String, dynamic> json) => AccessPoint(
      id: json['id'] as String,
      name: json['name'] as String,
      location: json['location'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      isLocked: json['isLocked'] as bool,
      lastAccess: json['lastAccess'] == null
          ? null
          : DateTime.parse(json['lastAccess'] as String),
    );

Map<String, dynamic> _$AccessPointToJson(AccessPoint instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'location': instance.location,
      'type': instance.type,
      'status': instance.status,
      'isLocked': instance.isLocked,
      'lastAccess': instance.lastAccess?.toIso8601String(),
    };

SecurityIncidents _$SecurityIncidentsFromJson(Map<String, dynamic> json) =>
    SecurityIncidents(
      openIncidents: (json['openIncidents'] as num).toInt(),
      todayIncidents: (json['todayIncidents'] as num).toInt(),
      weeklyResolved: (json['weeklyResolved'] as num).toInt(),
      recentIncidents: (json['recentIncidents'] as List<dynamic>)
          .map((e) => SecurityIncident.fromJson(e as Map<String, dynamic>))
          .toList(),
      statistics: IncidentStatistics.fromJson(
          json['statistics'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SecurityIncidentsToJson(SecurityIncidents instance) =>
    <String, dynamic>{
      'openIncidents': instance.openIncidents,
      'todayIncidents': instance.todayIncidents,
      'weeklyResolved': instance.weeklyResolved,
      'recentIncidents': instance.recentIncidents,
      'statistics': instance.statistics,
    };

SecurityIncident _$SecurityIncidentFromJson(Map<String, dynamic> json) =>
    SecurityIncident(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      severity: json['severity'] as String,
      status: json['status'] as String,
      location: json['location'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      reportedBy: json['reportedBy'] as String?,
      resolvedAt: json['resolvedAt'] == null
          ? null
          : DateTime.parse(json['resolvedAt'] as String),
    );

Map<String, dynamic> _$SecurityIncidentToJson(SecurityIncident instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'severity': instance.severity,
      'status': instance.status,
      'location': instance.location,
      'timestamp': instance.timestamp.toIso8601String(),
      'reportedBy': instance.reportedBy,
      'resolvedAt': instance.resolvedAt?.toIso8601String(),
    };

IncidentStatistics _$IncidentStatisticsFromJson(Map<String, dynamic> json) =>
    IncidentStatistics(
      byType: Map<String, int>.from(json['byType'] as Map),
      bySeverity: Map<String, int>.from(json['bySeverity'] as Map),
      byStatus: Map<String, int>.from(json['byStatus'] as Map),
    );

Map<String, dynamic> _$IncidentStatisticsToJson(IncidentStatistics instance) =>
    <String, dynamic>{
      'byType': instance.byType,
      'bySeverity': instance.bySeverity,
      'byStatus': instance.byStatus,
    };

SecurityMaintenance _$SecurityMaintenanceFromJson(Map<String, dynamic> json) =>
    SecurityMaintenance(
      overdueTasks: (json['overdueTasks'] as num).toInt(),
      dueSoonTasks: (json['dueSoonTasks'] as num).toInt(),
      completedTasks: (json['completedTasks'] as num).toInt(),
      upcomingMaintenance: (json['upcomingMaintenance'] as List<dynamic>)
          .map((e) =>
              SecurityMaintenanceTask.fromJson(e as Map<String, dynamic>))
          .toList(),
      maintenanceHistory: (json['maintenanceHistory'] as List<dynamic>)
          .map((e) =>
              SecurityMaintenanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SecurityMaintenanceToJson(
        SecurityMaintenance instance) =>
    <String, dynamic>{
      'overdueTasks': instance.overdueTasks,
      'dueSoonTasks': instance.dueSoonTasks,
      'completedTasks': instance.completedTasks,
      'upcomingMaintenance': instance.upcomingMaintenance,
      'maintenanceHistory': instance.maintenanceHistory,
    };

SecurityMaintenanceTask _$SecurityMaintenanceTaskFromJson(
        Map<String, dynamic> json) =>
    SecurityMaintenanceTask(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      dueDate: DateTime.parse(json['dueDate'] as String),
      priority: json['priority'] as String,
      status: json['status'] as String,
      assignedTo: json['assignedTo'] as String?,
    );

Map<String, dynamic> _$SecurityMaintenanceTaskToJson(
        SecurityMaintenanceTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'dueDate': instance.dueDate.toIso8601String(),
      'priority': instance.priority,
      'status': instance.status,
      'assignedTo': instance.assignedTo,
    };

SecurityMaintenanceRecord _$SecurityMaintenanceRecordFromJson(
        Map<String, dynamic> json) =>
    SecurityMaintenanceRecord(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      completedDate: DateTime.parse(json['completedDate'] as String),
      cost: (json['cost'] as num?)?.toDouble(),
      technician: json['technician'] as String,
      notes: json['notes'] as String,
    );

Map<String, dynamic> _$SecurityMaintenanceRecordToJson(
        SecurityMaintenanceRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'type': instance.type,
      'completedDate': instance.completedDate.toIso8601String(),
      'cost': instance.cost,
      'technician': instance.technician,
      'notes': instance.notes,
    };

PowerAnalytics _$PowerAnalyticsFromJson(Map<String, dynamic> json) =>
    PowerAnalytics(
      totalConsumption: (json['totalConsumption'] as num).toDouble(),
      averageConsumption: (json['averageConsumption'] as num).toDouble(),
      peakConsumption: (json['peakConsumption'] as num).toDouble(),
      costToday: (json['costToday'] as num).toDouble(),
      costThisMonth: (json['costThisMonth'] as num).toDouble(),
      efficiencyRating: (json['efficiencyRating'] as num).toDouble(),
      hourlyUsage: (json['hourlyUsage'] as List<dynamic>)
          .map((e) => PowerUsageData.fromJson(e as Map<String, dynamic>))
          .toList(),
      dailyUsage: (json['dailyUsage'] as List<dynamic>)
          .map((e) => PowerUsageData.fromJson(e as Map<String, dynamic>))
          .toList(),
      generatorUsage: (json['generatorUsage'] as List<dynamic>)
          .map((e) => GeneratorUsage.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PowerAnalyticsToJson(PowerAnalytics instance) =>
    <String, dynamic>{
      'totalConsumption': instance.totalConsumption,
      'averageConsumption': instance.averageConsumption,
      'peakConsumption': instance.peakConsumption,
      'costToday': instance.costToday,
      'costThisMonth': instance.costThisMonth,
      'efficiencyRating': instance.efficiencyRating,
      'hourlyUsage': instance.hourlyUsage,
      'dailyUsage': instance.dailyUsage,
      'generatorUsage': instance.generatorUsage,
    };

PowerUsageData _$PowerUsageDataFromJson(Map<String, dynamic> json) =>
    PowerUsageData(
      timestamp: json['timestamp'] as String,
      consumption: (json['consumption'] as num).toDouble(),
      cost: (json['cost'] as num).toDouble(),
    );

Map<String, dynamic> _$PowerUsageDataToJson(PowerUsageData instance) =>
    <String, dynamic>{
      'timestamp': instance.timestamp,
      'consumption': instance.consumption,
      'cost': instance.cost,
    };

GeneratorUsage _$GeneratorUsageFromJson(Map<String, dynamic> json) =>
    GeneratorUsage(
      generatorName: json['generatorName'] as String,
      hoursRun: (json['hoursRun'] as num).toDouble(),
      fuelConsumed: (json['fuelConsumed'] as num).toDouble(),
      powerGenerated: (json['powerGenerated'] as num).toDouble(),
      efficiency: (json['efficiency'] as num).toDouble(),
    );

Map<String, dynamic> _$GeneratorUsageToJson(GeneratorUsage instance) =>
    <String, dynamic>{
      'generatorName': instance.generatorName,
      'hoursRun': instance.hoursRun,
      'fuelConsumed': instance.fuelConsumed,
      'powerGenerated': instance.powerGenerated,
      'efficiency': instance.efficiency,
    };

ElectricityMaintenance _$ElectricityMaintenanceFromJson(
        Map<String, dynamic> json) =>
    ElectricityMaintenance(
      overdueTasks: (json['overdueTasks'] as num).toInt(),
      dueSoonTasks: (json['dueSoonTasks'] as num).toInt(),
      completedTasks: (json['completedTasks'] as num).toInt(),
      upcomingMaintenance: (json['upcomingMaintenance'] as List<dynamic>)
          .map((e) =>
              ElectricityMaintenanceTask.fromJson(e as Map<String, dynamic>))
          .toList(),
      maintenanceHistory: (json['maintenanceHistory'] as List<dynamic>)
          .map((e) =>
              ElectricityMaintenanceRecord.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ElectricityMaintenanceToJson(
        ElectricityMaintenance instance) =>
    <String, dynamic>{
      'overdueTasks': instance.overdueTasks,
      'dueSoonTasks': instance.dueSoonTasks,
      'completedTasks': instance.completedTasks,
      'upcomingMaintenance': instance.upcomingMaintenance,
      'maintenanceHistory': instance.maintenanceHistory,
    };

ElectricityMaintenanceTask _$ElectricityMaintenanceTaskFromJson(
        Map<String, dynamic> json) =>
    ElectricityMaintenanceTask(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      dueDate: DateTime.parse(json['dueDate'] as String),
      priority: json['priority'] as String,
      status: json['status'] as String,
      assignedTo: json['assignedTo'] as String?,
    );

Map<String, dynamic> _$ElectricityMaintenanceTaskToJson(
        ElectricityMaintenanceTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'dueDate': instance.dueDate.toIso8601String(),
      'priority': instance.priority,
      'status': instance.status,
      'assignedTo': instance.assignedTo,
    };

ElectricityMaintenanceRecord _$ElectricityMaintenanceRecordFromJson(
        Map<String, dynamic> json) =>
    ElectricityMaintenanceRecord(
      id: json['id'] as String,
      title: json['title'] as String,
      type: json['type'] as String,
      completedDate: DateTime.parse(json['completedDate'] as String),
      cost: (json['cost'] as num?)?.toDouble(),
      technician: json['technician'] as String,
      notes: json['notes'] as String,
    );

Map<String, dynamic> _$ElectricityMaintenanceRecordToJson(
        ElectricityMaintenanceRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'type': instance.type,
      'completedDate': instance.completedDate.toIso8601String(),
      'cost': instance.cost,
      'technician': instance.technician,
      'notes': instance.notes,
    };

LoadManagement _$LoadManagementFromJson(Map<String, dynamic> json) =>
    LoadManagement(
      currentLoad: (json['currentLoad'] as num).toDouble(),
      maxCapacity: (json['maxCapacity'] as num).toDouble(),
      loadPercentage: (json['loadPercentage'] as num).toDouble(),
      loadZones: (json['loadZones'] as List<dynamic>)
          .map((e) => LoadZone.fromJson(e as Map<String, dynamic>))
          .toList(),
      schedules: (json['schedules'] as List<dynamic>)
          .map((e) => LoadSchedule.fromJson(e as Map<String, dynamic>))
          .toList(),
      alerts: (json['alerts'] as List<dynamic>)
          .map((e) => LoadAlert.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LoadManagementToJson(LoadManagement instance) =>
    <String, dynamic>{
      'currentLoad': instance.currentLoad,
      'maxCapacity': instance.maxCapacity,
      'loadPercentage': instance.loadPercentage,
      'loadZones': instance.loadZones,
      'schedules': instance.schedules,
      'alerts': instance.alerts,
    };

LoadZone _$LoadZoneFromJson(Map<String, dynamic> json) => LoadZone(
      id: json['id'] as String,
      name: json['name'] as String,
      currentLoad: (json['currentLoad'] as num).toDouble(),
      maxLoad: (json['maxLoad'] as num).toDouble(),
      status: json['status'] as String,
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$LoadZoneToJson(LoadZone instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'currentLoad': instance.currentLoad,
      'maxLoad': instance.maxLoad,
      'status': instance.status,
      'isActive': instance.isActive,
    };

LoadSchedule _$LoadScheduleFromJson(Map<String, dynamic> json) => LoadSchedule(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      affectedZones: (json['affectedZones'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$LoadScheduleToJson(LoadSchedule instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'affectedZones': instance.affectedZones,
      'isActive': instance.isActive,
    };

LoadAlert _$LoadAlertFromJson(Map<String, dynamic> json) => LoadAlert(
      id: json['id'] as String,
      message: json['message'] as String,
      severity: json['severity'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      affectedZone: json['affectedZone'] as String?,
    );

Map<String, dynamic> _$LoadAlertToJson(LoadAlert instance) => <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'severity': instance.severity,
      'timestamp': instance.timestamp.toIso8601String(),
      'affectedZone': instance.affectedZone,
    };

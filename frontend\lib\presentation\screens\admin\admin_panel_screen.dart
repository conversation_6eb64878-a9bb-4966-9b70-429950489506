import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/permission_wrapper.dart' as wrapper;
import '../../widgets/admin_dialogs.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/constants/api_constants.dart';
import '../../../core/services/admin_service.dart';
import '../../../data/models/user_model.dart';
import '../../../core/services/service_locator.dart';
import '../../providers/admin_providers.dart';
// Removed property_system_management_screen.dart import - now accessible via property settings

class AdminPanelScreen extends StatefulWidget {
  const AdminPanelScreen({Key? key}) : super(key: key);

  @override
  State<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends State<AdminPanelScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('Admin Panel'),
          backgroundColor: Color(AppConstants.primaryColor),
          foregroundColor: Colors.white,
          bottom: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(text: 'User Management', icon: Icon(Icons.people)),
              Tab(text: 'Role Management', icon: Icon(Icons.admin_panel_settings)),
              Tab(text: 'Permissions', icon: Icon(Icons.security)),
            ],
          ),
        ),
        body: TabBarView(
          controller: _tabController,
          children: const [
            UserManagementTab(),
            RoleManagementTab(),
            PermissionsTab(),
          ],
        ),
      );
  }
}

class UserManagementTab extends StatefulWidget {
  const UserManagementTab({Key? key}) : super(key: key);

  @override
  State<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends State<UserManagementTab> {
  List<UserModel> users = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() => isLoading = true);
    try {
      final response = await serviceLocator.apiClient.get(
        ApiConstants.usersList,
      );

      if (response.isSuccess && response.data != null) {
        final usersData = response.data['data'] as List<dynamic>;
        setState(() {
          users = usersData.map((userData) => UserModel(
            id: userData['id'],
            name: userData['name'],
            email: userData['email'],
            role: _mapStringToUserRole(userData['role']),
            isActive: userData['isActive'],
            createdAt: DateTime.parse(userData['createdAt']),
            updatedAt: DateTime.parse(userData['updatedAt']),
          )).toList();
          isLoading = false;
        });
      } else {
        setState(() => isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error loading users: ${response.message}')),
          );
        }
      }
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading users: $e')),
        );
      }
    }
  }

  UserRole _mapStringToUserRole(String role) {
    switch (role.toUpperCase()) {
      case 'SUPER_ADMIN':
        return UserRole.superAdmin;
      case 'PROPERTY_MANAGER':
        return UserRole.propertyManager;
      case 'OFFICE_MANAGER':
        return UserRole.officeManager;
      case 'SECURITY_PERSONNEL':
        return UserRole.securityPersonnel;
      case 'MAINTENANCE_STAFF':
        return UserRole.maintenanceStaff;
      case 'CONSTRUCTION_SUPERVISOR':
        return UserRole.constructionSupervisor;
      default:
        return UserRole.propertyManager;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Users (${users.length})',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              wrapper.PermissionWrapper(
                permission: 'employees.create',
                child: ElevatedButton.icon(
                  onPressed: () => _showCreateUserDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add User'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.primaryColor),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(AppConstants.primaryColor),
                      child: Text(
                        user.name.substring(0, 1).toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(user.name),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(user.email),
                        Text(
                          _getRoleDisplayName(user.role),
                          style: TextStyle(
                            color: _getRoleColor(user.role),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Switch(
                          value: user.isActive,
                          onChanged: (value) => _toggleUserStatus(user, value),
                          activeColor: Color(AppConstants.primaryColor),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) => _handleUserAction(user, value),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit),
                                  SizedBox(width: 8),
                                  Text('Edit'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'permissions',
                              child: Row(
                                children: [
                                  Icon(Icons.security),
                                  SizedBox(width: 8),
                                  Text('Permissions'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Delete', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'Super Administrator';
      case UserRole.propertyManager:
        return 'Property Manager';
      case UserRole.officeManager:
        return 'Office Manager';
      case UserRole.securityPersonnel:
        return 'Security Personnel';
      case UserRole.maintenanceStaff:
        return 'Maintenance Staff';
      case UserRole.constructionSupervisor:
        return 'Construction Supervisor';
    }
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return Colors.red;
      case UserRole.propertyManager:
        return Colors.blue;
      case UserRole.officeManager:
        return Colors.green;
      case UserRole.securityPersonnel:
        return Colors.orange;
      case UserRole.maintenanceStaff:
        return Colors.purple;
      case UserRole.constructionSupervisor:
        return Colors.teal;
    }
  }

  void _showCreateUserDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateUserDialog(),
    );
  }

  void _toggleUserStatus(UserModel user, bool isActive) {
    // TODO: Implement API call to toggle user status
    setState(() {
      final index = users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        users[index] = user.copyWith(isActive: isActive);
      }
    });
  }

  void _handleUserAction(UserModel user, String action) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'permissions':
        _showUserPermissionsDialog(user);
        break;
      case 'delete':
        _showDeleteUserDialog(user);
        break;
    }
  }

  void _showEditUserDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => EditUserDialog(user: user),
    );
  }

  void _showUserPermissionsDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => UserPermissionsDialog(user: user),
    );
  }

  void _showDeleteUserDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // TODO: Implement delete user API call
              Navigator.pop(context);
              setState(() {
                users.removeWhere((u) => u.id == user.id);
              });
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class RoleManagementTab extends ConsumerWidget {
  const RoleManagementTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final rolesAsync = ref.watch(rolesProvider);
    final roleFormState = ref.watch(roleFormProvider);

    return Scaffold(
      body: rolesAsync.when(
        data: (roles) => _buildRolesList(context, ref, roles),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Error: $error'),
              ElevatedButton(
                onPressed: () => ref.refresh(rolesProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateRoleDialog(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildRolesList(BuildContext context, WidgetRef ref, List<Role> roles) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: roles.length,
      itemBuilder: (context, index) {
        final role = roles[index];
        return Card(
          child: ListTile(
            title: Text(role.name),
            subtitle: Text(role.description ?? 'No description'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${role.userCount} users'),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditRoleDialog(context, ref, role),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _showDeleteRoleDialog(context, ref, role),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showCreateRoleDialog(BuildContext context, WidgetRef ref) {
    // TODO: Implement create role dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Create role dialog - To be implemented')),
    );
  }

  void _showEditRoleDialog(BuildContext context, WidgetRef ref, Role role) {
    // TODO: Implement edit role dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit role: ${role.name} - To be implemented')),
    );
  }

  void _showDeleteRoleDialog(BuildContext context, WidgetRef ref, Role role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Role'),
        content: Text('Are you sure you want to delete the role "${role.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(roleFormProvider.notifier).deleteRole(role.id);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class PermissionsTab extends ConsumerWidget {
  const PermissionsTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionsAsync = ref.watch(permissionsProvider);

    return permissionsAsync.when(
      data: (permissions) => _buildPermissionsList(context, permissions),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Error: $error'),
            ElevatedButton(
              onPressed: () => ref.refresh(permissionsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsList(BuildContext context, List<Permission> permissions) {
    // Group permissions by category
    final groupedPermissions = <String, List<Permission>>{};
    for (final permission in permissions) {
      final category = permission.category ?? 'General';
      groupedPermissions.putIfAbsent(category, () => []).add(permission);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedPermissions.keys.length,
      itemBuilder: (context, index) {
        final category = groupedPermissions.keys.elementAt(index);
        final categoryPermissions = groupedPermissions[category]!;

        return Card(
          child: ExpansionTile(
            title: Text(category),
            subtitle: Text('${categoryPermissions.length} permissions'),
            children: categoryPermissions.map((permission) {
              return ListTile(
                title: Text(permission.name),
                subtitle: Text(permission.description ?? 'No description'),
                trailing: Text('${permission.roleCount} roles'),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}

class CreateUserDialog extends StatelessWidget {
  const CreateUserDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New User'),
      content: const Text('User creation form - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Create'),
        ),
      ],
    );
  }
}

class EditUserDialog extends StatelessWidget {
  final UserModel user;

  const EditUserDialog({Key? key, required this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit User'),
      content: Text('Edit form for ${user.name} - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Save'),
        ),
      ],
    );
  }
}

class UserPermissionsDialog extends StatelessWidget {
  final UserModel user;

  const UserPermissionsDialog({Key? key, required this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${user.name} Permissions'),
      content: Text('Permissions management for ${user.name} - Coming Soon'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

// PropertySystemManagementTab removed - now accessible via property settings gear icon

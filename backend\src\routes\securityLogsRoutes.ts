import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getSecurityLogs,
  getSecurityLogById,
  createSecurityLog,
  updateSecurityLog,
  deleteSecurityLog
} from '../controllers/securityLogsController';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Security logs routes
router.get('/', getSecurityLogs);
router.get('/:id', getSecurityLogById);
router.post('/', createSecurityLog);
router.put('/:id', updateSecurityLog);
router.delete('/:id', deleteSecurityLog);

export default router;

import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getConstructionSites,
  getConstructionSiteById,
  createConstructionSite,
  updateConstructionSite,
  deleteConstructionSite,
  recordAttendance,
  getAttendance
} from '../controllers/constructionSitesController';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Construction sites routes
router.get('/', getConstructionSites);
router.get('/:id', getConstructionSiteById);
router.post('/', createConstructionSite);
router.put('/:id', updateConstructionSite);
router.delete('/:id', deleteConstructionSite);

// Attendance routes
router.post('/:id/attendance', recordAttendance);
router.get('/:id/attendance', getAttendance);

export default router;

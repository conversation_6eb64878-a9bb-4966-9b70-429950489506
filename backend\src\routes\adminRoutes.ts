import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getPermissions,
  assignUserRole,
  removeUserRole
} from '../controllers/adminController';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Role management routes
router.get('/roles', getRoles);
router.get('/roles/:id', getRoleById);
router.post('/roles', createRole);
router.put('/roles/:id', updateRole);
router.delete('/roles/:id', deleteRole);

// Permission management routes
router.get('/permissions', getPermissions);

// User role assignment routes
router.post('/user-roles', assignUserRole);
router.delete('/user-roles/:userId/:roleId', removeUserRole);

export default router;

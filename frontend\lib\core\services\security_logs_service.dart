
import '../constants/api_constants.dart';
import '../network/api_client.dart';
import '../utils/logger.dart';
import '../utils/api_response.dart';

class SecurityLogsService {
  final ApiClient _apiClient;

  SecurityLogsService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();

  // Get security logs
  Future<ApiResponse<PaginatedSecurityLogs>> getSecurityLogs({
    String? propertyId,
    String? startDate,
    String? endDate,
    String? status,
    String? guardName,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'limit': limit.toString(),
      };
      
      if (propertyId != null) queryParams['propertyId'] = propertyId;
      if (startDate != null) queryParams['startDate'] = startDate;
      if (endDate != null) queryParams['endDate'] = endDate;
      if (status != null) queryParams['status'] = status;
      if (guardName != null) queryParams['guardName'] = guardName;

      final response = await _apiClient.get(
        ApiConstants.securityLogs,
        queryParameters: queryParams,
      );

      if (response.data['success'] == true) {
        return ApiResponse<PaginatedSecurityLogs>(
          success: true,
          data: PaginatedSecurityLogs.fromJson(response.data),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch security logs');
      }
    } catch (e) {
      Logger.error('Error fetching security logs: $e');
      return ApiResponse.error('Failed to fetch security logs');
    }
  }

  // Get security log by ID
  Future<ApiResponse<SecurityLog>> getSecurityLogById(String logId) async {
    try {
      final response = await _apiClient.get(
        '${ApiConstants.securityLogs}/$logId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<SecurityLog>(
          success: true,
          data: SecurityLog.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to fetch security log');
      }
    } catch (e) {
      Logger.error('Error fetching security log: $e');
      return ApiResponse.error('Failed to fetch security log');
    }
  }

  // Create security log
  Future<ApiResponse<SecurityLog>> createSecurityLog({
    required String propertyId,
    required String activity,
    required String location,
    required String guardName,
    required String status,
    String? notes,
    String? timestamp,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiConstants.securityLogs,
        data: {
          'propertyId': propertyId,
          'activity': activity,
          'location': location,
          'guardName': guardName,
          'status': status,
          'notes': notes,
          'timestamp': timestamp,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<SecurityLog>(
          success: true,
          data: SecurityLog.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to create security log');
      }
    } catch (e) {
      Logger.error('Error creating security log: $e');
      return ApiResponse.error('Failed to create security log');
    }
  }

  // Update security log
  Future<ApiResponse<SecurityLog>> updateSecurityLog({
    required String logId,
    String? activity,
    String? location,
    String? guardName,
    String? status,
    String? notes,
  }) async {
    try {
      final response = await _apiClient.put(
        '${ApiConstants.securityLogs}/$logId',
        data: {
          if (activity != null) 'activity': activity,
          if (location != null) 'location': location,
          if (guardName != null) 'guardName': guardName,
          if (status != null) 'status': status,
          if (notes != null) 'notes': notes,
        },
      );

      if (response.data['success'] == true) {
        return ApiResponse<SecurityLog>(
          success: true,
          data: SecurityLog.fromJson(response.data['data']),
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to update security log');
      }
    } catch (e) {
      Logger.error('Error updating security log: $e');
      return ApiResponse.error('Failed to update security log');
    }
  }

  // Delete security log
  Future<ApiResponse<void>> deleteSecurityLog(String logId) async {
    try {
      final response = await _apiClient.delete(
        '${ApiConstants.securityLogs}/$logId',
      );

      if (response.data['success'] == true) {
        return ApiResponse<void>(
          success: true,
          message: response.data['message'],
        );
      } else {
        return ApiResponse.error(response.data['message'] ?? 'Failed to delete security log');
      }
    } catch (e) {
      Logger.error('Error deleting security log: $e');
      return ApiResponse.error('Failed to delete security log');
    }
  }
}

// Security Log Models
class SecurityLog {
  final String id;
  final String propertyId;
  final String activity;
  final String location;
  final String guardName;
  final String status;
  final String? notes;
  final DateTime timestamp;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Property? property;

  SecurityLog({
    required this.id,
    required this.propertyId,
    required this.activity,
    required this.location,
    required this.guardName,
    required this.status,
    this.notes,
    required this.timestamp,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.property,
  });

  factory SecurityLog.fromJson(Map<String, dynamic> json) {
    return SecurityLog(
      id: json['id'],
      propertyId: json['propertyId'],
      activity: json['activity'],
      location: json['location'],
      guardName: json['guardName'],
      status: json['status'],
      notes: json['notes'],
      timestamp: DateTime.parse(json['timestamp']),
      createdBy: json['createdBy'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      property: json['property'] != null ? Property.fromJson(json['property']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'propertyId': propertyId,
      'activity': activity,
      'location': location,
      'guardName': guardName,
      'status': status,
      'notes': notes,
      'timestamp': timestamp.toIso8601String(),
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'property': property?.toJson(),
    };
  }
}

class Property {
  final String id;
  final String name;

  Property({
    required this.id,
    required this.name,
  });

  factory Property.fromJson(Map<String, dynamic> json) {
    return Property(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class PaginatedSecurityLogs {
  final List<SecurityLog> data;
  final Pagination pagination;

  PaginatedSecurityLogs({
    required this.data,
    required this.pagination,
  });

  factory PaginatedSecurityLogs.fromJson(Map<String, dynamic> json) {
    return PaginatedSecurityLogs(
      data: (json['data'] as List<dynamic>)
          .map((log) => SecurityLog.fromJson(log))
          .toList(),
      pagination: Pagination.fromJson(json['pagination']),
    );
  }
}

class Pagination {
  final int page;
  final int limit;
  final int total;
  final int totalPages;

  Pagination({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      page: json['page'],
      limit: json['limit'],
      total: json['total'],
      totalPages: json['totalPages'],
    );
  }
}

// Security Log Status enum
enum SecurityLogStatus {
  normal,
  logged,
  completed,
  verified,
  alert,
}

extension SecurityLogStatusExtension on SecurityLogStatus {
  String get value {
    switch (this) {
      case SecurityLogStatus.normal:
        return 'NORMAL';
      case SecurityLogStatus.logged:
        return 'LOGGED';
      case SecurityLogStatus.completed:
        return 'COMPLETED';
      case SecurityLogStatus.verified:
        return 'VERIFIED';
      case SecurityLogStatus.alert:
        return 'ALERT';
    }
  }

  String get displayName {
    switch (this) {
      case SecurityLogStatus.normal:
        return 'Normal';
      case SecurityLogStatus.logged:
        return 'Logged';
      case SecurityLogStatus.completed:
        return 'Completed';
      case SecurityLogStatus.verified:
        return 'Verified';
      case SecurityLogStatus.alert:
        return 'Alert';
    }
  }
}

// Helper function to get status from string
SecurityLogStatus? getSecurityLogStatusFromString(String status) {
  switch (status.toUpperCase()) {
    case 'NORMAL':
      return SecurityLogStatus.normal;
    case 'LOGGED':
      return SecurityLogStatus.logged;
    case 'COMPLETED':
      return SecurityLogStatus.completed;
    case 'VERIFIED':
      return SecurityLogStatus.verified;
    case 'ALERT':
      return SecurityLogStatus.alert;
    default:
      return null;
  }
}

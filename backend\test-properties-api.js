const { PrismaClient } = require('@prisma/client');

async function testPropertiesAPI() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Testing Properties API Logic...');
    
    // Check admin user
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminUser) {
      console.log('❌ Admin user not found!');
      return;
    }
    
    console.log(`✅ Admin user found: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Assigned Properties: ${adminUser.assignedProperties.length}`);
    
    // Check all properties
    const allProperties = await prisma.property.findMany({
      select: {
        id: true,
        name: true,
        type: true,
        status: true,
      }
    });
    
    console.log(`\n📊 Total properties in database: ${allProperties.length}`);
    
    // Simulate the API logic
    let whereClause = {};
    
    // Super admins can see all properties
    if (adminUser.role !== 'SUPER_ADMIN') {
      console.log('⚠️  Admin is not SUPER_ADMIN, applying property filter...');
      whereClause.id = {
        in: adminUser.assignedProperties,
      };
    } else {
      console.log('✅ Admin is SUPER_ADMIN, no property filter applied');
    }
    
    console.log('🔍 Where clause:', JSON.stringify(whereClause, null, 2));
    
    // Get properties with the same logic as API
    const filteredProperties = await prisma.property.findMany({
      where: whereClause,
      include: {
        systemStatuses: {
          select: {
            systemType: true,
            status: true,
            healthScore: true,
            lastChecked: true,
          },
        },
        _count: {
          select: {
            alerts: {
              where: {
                status: 'OPEN',
              },
            },
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });
    
    console.log(`\n📋 Properties returned by API logic: ${filteredProperties.length}`);
    filteredProperties.forEach((prop, index) => {
      console.log(`${index + 1}. ${prop.name} (${prop.type})`);
    });
    
    if (filteredProperties.length === 0 && allProperties.length > 0) {
      console.log('\n❌ ISSUE FOUND: API would return 0 properties but database has properties!');
      
      if (adminUser.role !== 'SUPER_ADMIN') {
        console.log('💡 SOLUTION: Admin user needs SUPER_ADMIN role or assigned properties');
        
        // Fix the admin user role
        console.log('🔧 Fixing admin user role...');
        await prisma.user.update({
          where: { id: adminUser.id },
          data: { role: 'SUPER_ADMIN' }
        });
        console.log('✅ Admin user role updated to SUPER_ADMIN');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPropertiesAPI();

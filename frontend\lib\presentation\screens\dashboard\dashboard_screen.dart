import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/dashboard.dart';
import '../../providers/dashboard_providers.dart';
import '../main/main_navigation_screen.dart';
import 'widgets/dashboard_header.dart';
import 'widgets/system_status_card.dart';
import 'widgets/property_grid.dart';
import 'widgets/alerts_feed.dart';
import 'widgets/system_health_chart.dart';
import 'widgets/quick_actions.dart';
import 'widgets/performance_metrics.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  Timer? _refreshTimer;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _startAutoRefresh() {
    final autoRefresh = ref.read(autoRefreshProvider);
    if (autoRefresh) {
      _refreshTimer?.cancel();
      _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        if (mounted) {
          _handleRefresh();
        }
      });
    }
  }

  Future<void> _handleRefresh() async {
    final params = DashboardParams(
      propertyIds: ref.read(propertyFilterProvider),
      timeRange: ref.read(dashboardTimeRangeProvider),
    );

    // Refresh the providers
    ref.refresh(dashboardOverviewProvider(params));
    ref.refresh(dashboardAlertsProvider(const AlertParams()));

    // Wait a bit for the refresh to complete
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    final params = DashboardParams(
      propertyIds: ref.watch(propertyFilterProvider),
      timeRange: ref.watch(dashboardTimeRangeProvider),
    );

    final dashboardAsyncValue = ref.watch(dashboardOverviewProvider(params));
    final alertsAsyncValue = ref.watch(dashboardAlertsProvider(const AlertParams()));

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Dashboard',
        actions: [
          // Time Range Selector
          PopupMenuButton<String>(
            icon: const Icon(Icons.schedule),
            onSelected: (timeRange) {
              ref.read(dashboardTimeRangeProvider.notifier).setTimeRange(timeRange);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: '24h', child: Text('Last 24 Hours')),
              const PopupMenuItem(value: '7d', child: Text('Last 7 Days')),
              const PopupMenuItem(value: '30d', child: Text('Last 30 Days')),
              const PopupMenuItem(value: '90d', child: Text('Last 90 Days')),
            ],
          ),
          // Auto Refresh Toggle
          IconButton(
            icon: Icon(
              ref.watch(autoRefreshProvider) ? Icons.sync : Icons.sync_disabled,
              color: ref.watch(autoRefreshProvider) ? AppTheme.primaryColor : null,
            ),
            onPressed: () {
              ref.read(autoRefreshProvider.notifier).toggle();
              _startAutoRefresh();
            },
          ),
          // Refresh Button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _handleRefresh,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: SingleChildScrollView(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: dashboardAsyncValue.when(
            data: (dashboard) => _buildDashboardContent(context, dashboard, alertsAsyncValue),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
                  const SizedBox(height: 16),
                  Text('Failed to load dashboard: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _handleRefresh,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context, DashboardOverview dashboard, AsyncValue alertsAsyncValue) {
    final stats = ref.read(dashboardStatsProvider(dashboard));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Dashboard Header with Key Metrics
        DashboardHeader(
          overview: dashboard,
          stats: stats,
        ),

        const SizedBox(height: 20),

        // Quick Actions
        const QuickActions(),

        const SizedBox(height: 20),

        // System Status Grid
        SystemStatusCard(
          systemStatuses: dashboard.systemStatuses,
          stats: stats,
        ),

        const SizedBox(height: 20),

        // Properties Grid
        PropertyGrid(
          properties: dashboard.properties,
          onPropertyTap: (propertyId) {
            // Navigate to property detail screen
            context.go('/properties/$propertyId');
          },
        ),

        const SizedBox(height: 20),

        // Row with Charts and Alerts
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // System Health Chart
            Expanded(
              flex: 1,
              child: SystemHealthChart(
                systemStatuses: dashboard.systemStatuses,
                stats: stats,
              ),
            ),

            const SizedBox(width: 20),

            // Alerts Feed
            Expanded(
              flex: 1,
              child: alertsAsyncValue.when(
                data: (alerts) => AlertsFeed(
                  alerts: alerts,
                  onAlertTap: (alertId) {
                    // Navigate to alert detail screen
                    context.go('/alerts/$alertId');
                  },
                  onViewAllTap: () {
                    // Navigate to alerts list screen
                    context.go('/alerts');
                  },
                ),
                loading: () => const Card(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: Center(child: CircularProgressIndicator()),
                  ),
                ),
                error: (error, stack) => Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Center(
                      child: Text('Failed to load alerts: $error'),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Performance Metrics
        PerformanceMetrics(
          metrics: dashboard.statistics.metrics,
          timeRange: ref.read(dashboardTimeRangeProvider),
        ),

        const SizedBox(height: 20),
      ],
    );
  }
}

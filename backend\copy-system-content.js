const { PrismaClient } = require('@prisma/client');

async function copySystemContent() {
  const prisma = new PrismaClient();
  
  try {
    console.log('=== CHECKING PROPERTIES ===');
    const properties = await prisma.property.findMany({
      select: {
        id: true,
        name: true,
        type: true,
      }
    });
    
    console.log('Properties found:', properties.length);
    properties.forEach(prop => {
      console.log(`- ${prop.name} (${prop.type}) - ID: ${prop.id}`);
    });
    
    // Find Jubilee Hills Residence and Banjara Hills Apartment
    const jubileeHills = properties.find(p => p.name.includes('Jubilee Hills'));
    const banjaraHills = properties.find(p => p.name.includes('Banjara Hills'));
    
    if (!jubileeHills) {
      console.error('❌ Jubilee Hills Residence not found');
      return;
    }
    
    if (!banjaraHills) {
      console.error('❌ Banjara Hills Apartment not found');
      return;
    }
    
    console.log(`\n📋 Source: ${jubileeHills.name} (${jubileeHills.id})`);
    console.log(`📋 Target: ${banjaraHills.name} (${banjaraHills.id})`);
    
    // Get all system content from Jubilee Hills
    console.log('\n=== CHECKING SOURCE CONTENT ===');
    const sourceContent = await prisma.systemContent.findMany({
      where: {
        propertyId: jubileeHills.id,
        isActive: true
      },
      orderBy: [
        { systemType: 'asc' },
        { displayOrder: 'asc' },
        { createdAt: 'asc' }
      ]
    });
    
    console.log(`Found ${sourceContent.length} content items in Jubilee Hills`);
    
    // Group by system type
    const contentBySystem = {};
    sourceContent.forEach(content => {
      if (!contentBySystem[content.systemType]) {
        contentBySystem[content.systemType] = [];
      }
      contentBySystem[content.systemType].push(content);
    });
    
    console.log('\nContent by system type:');
    Object.keys(contentBySystem).forEach(systemType => {
      console.log(`  ${systemType}: ${contentBySystem[systemType].length} items`);
      contentBySystem[systemType].forEach(content => {
        console.log(`    - ${content.title} (${content.contentType})`);
      });
    });
    
    // Check existing content in Banjara Hills
    console.log('\n=== CHECKING TARGET CONTENT ===');
    const targetContent = await prisma.systemContent.findMany({
      where: {
        propertyId: banjaraHills.id,
        isActive: true
      }
    });
    
    console.log(`Found ${targetContent.length} content items in Banjara Hills`);
    
    // Copy content from Jubilee Hills to Banjara Hills
    console.log('\n=== COPYING CONTENT ===');
    let copiedCount = 0;
    let skippedCount = 0;
    
    for (const content of sourceContent) {
      // Check if similar content already exists in target
      const existingContent = await prisma.systemContent.findFirst({
        where: {
          propertyId: banjaraHills.id,
          systemType: content.systemType,
          contentType: content.contentType,
          title: content.title,
        }
      });
      
      if (existingContent) {
        console.log(`  ⏭️  Skipped: ${content.systemType} - ${content.title} (already exists)`);
        skippedCount++;
        continue;
      }
      
      // Create new content for Banjara Hills
      const newContent = await prisma.systemContent.create({
        data: {
          propertyId: banjaraHills.id,
          systemType: content.systemType,
          contentType: content.contentType,
          title: content.title,
          content: content.content,
          richContent: content.richContent,
          contentFormat: content.contentFormat,
          displayOrder: content.displayOrder,
          isActive: content.isActive,
          isTab: content.isTab,
          tabIcon: content.tabIcon,
          metadata: content.metadata || {},
        }
      });
      
      console.log(`  ✅ Copied: ${content.systemType} - ${content.title}`);
      copiedCount++;
    }
    
    console.log('\n=== COPY SUMMARY ===');
    console.log(`✅ Successfully copied: ${copiedCount} items`);
    console.log(`⏭️  Skipped (already exists): ${skippedCount} items`);
    console.log(`📊 Total processed: ${sourceContent.length} items`);
    
    // Verify the copy
    console.log('\n=== VERIFICATION ===');
    const newTargetContent = await prisma.systemContent.findMany({
      where: {
        propertyId: banjaraHills.id,
        isActive: true
      }
    });
    
    console.log(`Banjara Hills now has ${newTargetContent.length} content items`);
    
    const newContentBySystem = {};
    newTargetContent.forEach(content => {
      if (!newContentBySystem[content.systemType]) {
        newContentBySystem[content.systemType] = [];
      }
      newContentBySystem[content.systemType].push(content);
    });
    
    console.log('\nBanjara Hills content by system type:');
    Object.keys(newContentBySystem).forEach(systemType => {
      console.log(`  ${systemType}: ${newContentBySystem[systemType].length} items`);
    });
    
    console.log('\n🎉 Content copy completed successfully!');
    
  } catch (error) {
    console.error('❌ Error copying system content:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the copy operation
copySystemContent()
  .then(() => {
    console.log('✅ Copy operation completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Copy operation failed:', error);
    process.exit(1);
  });

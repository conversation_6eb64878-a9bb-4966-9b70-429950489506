import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/analytics_service.dart';
import '../../core/services/service_locator.dart';

// Service provider
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return getService<AnalyticsService>();
});

// Analytics query parameters
class AnalyticsQuery {
  final String? propertyId;
  final String? startDate;
  final String? endDate;
  final String? timeRange;

  AnalyticsQuery({
    this.propertyId,
    this.startDate,
    this.endDate,
    this.timeRange,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalyticsQuery &&
          runtimeType == other.runtimeType &&
          propertyId == other.propertyId &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          timeRange == other.timeRange;

  @override
  int get hashCode =>
      propertyId.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      timeRange.hashCode;
}

// Maintenance analytics provider
final maintenanceAnalyticsProvider = FutureProvider.family<MaintenanceAnalytics, AnalyticsQuery>((ref, query) async {
  final analyticsService = ref.read(analyticsServiceProvider);
  final response = await analyticsService.getMaintenanceAnalytics(
    propertyId: query.propertyId,
    startDate: query.startDate,
    endDate: query.endDate,
    timeRange: query.timeRange,
  );
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch maintenance analytics');
  }
});

// Security analytics provider
final securityAnalyticsProvider = FutureProvider.family<SecurityAnalytics, AnalyticsQuery>((ref, query) async {
  final analyticsService = ref.read(analyticsServiceProvider);
  final response = await analyticsService.getSecurityAnalytics(
    propertyId: query.propertyId,
    startDate: query.startDate,
    endDate: query.endDate,
    timeRange: query.timeRange,
  );
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.message ?? 'Failed to fetch security analytics');
  }
});

// Analytics filter state providers
final analyticsTimeRangeProvider = StateProvider<String>((ref) => '30d');
final analyticsPropertyFilterProvider = StateProvider<String?>((ref) => null);
final analyticsStartDateProvider = StateProvider<String?>((ref) => null);
final analyticsEndDateProvider = StateProvider<String?>((ref) => null);

// Combined analytics query provider
final analyticsQueryProvider = Provider<AnalyticsQuery>((ref) {
  final timeRange = ref.watch(analyticsTimeRangeProvider);
  final propertyId = ref.watch(analyticsPropertyFilterProvider);
  final startDate = ref.watch(analyticsStartDateProvider);
  final endDate = ref.watch(analyticsEndDateProvider);

  return AnalyticsQuery(
    propertyId: propertyId,
    startDate: startDate,
    endDate: endDate,
    timeRange: timeRange,
  );
});

// Convenience providers for current analytics data
final currentMaintenanceAnalyticsProvider = Provider<AsyncValue<MaintenanceAnalytics>>((ref) {
  final query = ref.watch(analyticsQueryProvider);
  return ref.watch(maintenanceAnalyticsProvider(query));
});

final currentSecurityAnalyticsProvider = Provider<AsyncValue<SecurityAnalytics>>((ref) {
  final query = ref.watch(analyticsQueryProvider);
  return ref.watch(securityAnalyticsProvider(query));
});

// Analytics refresh provider
final analyticsRefreshProvider = StateNotifierProvider<AnalyticsRefreshNotifier, bool>((ref) {
  return AnalyticsRefreshNotifier(ref);
});

class AnalyticsRefreshNotifier extends StateNotifier<bool> {
  final Ref _ref;

  AnalyticsRefreshNotifier(this._ref) : super(false);

  Future<void> refreshAnalytics() async {
    state = true;
    
    try {
      // Invalidate all analytics providers to force refresh
      _ref.invalidate(maintenanceAnalyticsProvider);
      _ref.invalidate(securityAnalyticsProvider);
      
      // Wait a bit for the refresh to complete
      await Future.delayed(const Duration(milliseconds: 500));
    } finally {
      state = false;
    }
  }
}

// Analytics view state provider
final analyticsViewProvider = StateProvider<AnalyticsView>((ref) => AnalyticsView.overview);

enum AnalyticsView {
  overview,
  trends,
  departments,
  performance,
}

extension AnalyticsViewExtension on AnalyticsView {
  String get displayName {
    switch (this) {
      case AnalyticsView.overview:
        return 'Overview';
      case AnalyticsView.trends:
        return 'Trends';
      case AnalyticsView.departments:
        return 'Departments';
      case AnalyticsView.performance:
        return 'Performance';
    }
  }

  String get value {
    switch (this) {
      case AnalyticsView.overview:
        return 'overview';
      case AnalyticsView.trends:
        return 'trends';
      case AnalyticsView.departments:
        return 'departments';
      case AnalyticsView.performance:
        return 'performance';
    }
  }
}

// Time range options
final timeRangeOptionsProvider = Provider<List<TimeRangeOption>>((ref) {
  return [
    TimeRangeOption(value: '24h', label: 'Last 24 Hours'),
    TimeRangeOption(value: '7d', label: 'Last 7 Days'),
    TimeRangeOption(value: '30d', label: 'Last 30 Days'),
    TimeRangeOption(value: '90d', label: 'Last 90 Days'),
  ];
});

class TimeRangeOption {
  final String value;
  final String label;

  TimeRangeOption({
    required this.value,
    required this.label,
  });
}

// Analytics export provider
final analyticsExportProvider = StateNotifierProvider<AnalyticsExportNotifier, AnalyticsExportState>((ref) {
  return AnalyticsExportNotifier();
});

class AnalyticsExportState {
  final bool isExporting;
  final String? error;
  final String? successMessage;

  AnalyticsExportState({
    this.isExporting = false,
    this.error,
    this.successMessage,
  });

  AnalyticsExportState copyWith({
    bool? isExporting,
    String? error,
    String? successMessage,
  }) {
    return AnalyticsExportState(
      isExporting: isExporting ?? this.isExporting,
      error: error,
      successMessage: successMessage,
    );
  }
}

class AnalyticsExportNotifier extends StateNotifier<AnalyticsExportState> {
  AnalyticsExportNotifier() : super(AnalyticsExportState());

  Future<void> exportMaintenanceAnalytics(MaintenanceAnalytics analytics) async {
    state = state.copyWith(isExporting: true, error: null);

    try {
      // TODO: Implement actual export functionality
      await Future.delayed(const Duration(seconds: 2)); // Simulate export
      
      state = state.copyWith(
        isExporting: false,
        successMessage: 'Analytics exported successfully',
      );
    } catch (e) {
      state = state.copyWith(
        isExporting: false,
        error: 'Failed to export analytics: $e',
      );
    }
  }

  Future<void> exportSecurityAnalytics(SecurityAnalytics analytics) async {
    state = state.copyWith(isExporting: true, error: null);

    try {
      // TODO: Implement actual export functionality
      await Future.delayed(const Duration(seconds: 2)); // Simulate export
      
      state = state.copyWith(
        isExporting: false,
        successMessage: 'Analytics exported successfully',
      );
    } catch (e) {
      state = state.copyWith(
        isExporting: false,
        error: 'Failed to export analytics: $e',
      );
    }
  }

  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}

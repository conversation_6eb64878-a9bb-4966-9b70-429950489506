import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'unified_system_management_screen.dart';

class WaterSystemScreen extends ConsumerWidget {
  final String propertyId;
  
  const WaterSystemScreen({
    super.key,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return UnifiedSystemManagementScreen(
      propertyId: propertyId,
      systemType: 'WATER',
      systemName: 'Water Management',
      systemIcon: Icons.water_drop,
      systemColor: Colors.blue,
    );
  }
}

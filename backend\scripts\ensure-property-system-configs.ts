import { PrismaClient, SystemType } from '@prisma/client';

const prisma = new PrismaClient();

async function ensurePropertySystemConfigs() {
  console.log('🔧 Starting property system configuration check...');

  try {
    // Get all properties
    const properties = await prisma.property.findMany({
      select: {
        id: true,
        name: true,
      },
    });

    console.log(`📋 Found ${properties.length} properties to check`);

    for (const property of properties) {
      console.log(`\n🏢 Checking property: ${property.name} (${property.id})`);

      // Check if property has system configurations
      const existingConfigs = await prisma.propertySystemConfig.findMany({
        where: { propertyId: property.id },
      });

      console.log(`   📊 Found ${existingConfigs.length} existing system configs`);

      if (existingConfigs.length === 0) {
        console.log(`   ➕ Creating default system configurations...`);

        // Create default system configurations
        const defaultSystemConfigs = [
          { systemType: SystemType.WATER, displayName: 'Water Management', displayOrder: 1 },
          { systemType: SystemType.ELECTRICITY, displayName: 'Electricity Management', displayOrder: 2 },
          { systemType: SystemType.SECURITY, displayName: 'Security Management', displayOrder: 3 },
          { systemType: SystemType.INTERNET, displayName: 'Internet Management', displayOrder: 4 },
          { systemType: SystemType.MAINTENANCE, displayName: 'Maintenance Management', displayOrder: 5 },
        ];

        const createdConfigs = await prisma.propertySystemConfig.createMany({
          data: defaultSystemConfigs.map(config => ({
            propertyId: property.id,
            ...config,
            isEnabled: true,
            configuration: {},
          })),
        });

        console.log(`   ✅ Created ${createdConfigs.count} system configurations`);
      } else {
        // Check if all required systems exist
        const existingSystemTypes = existingConfigs.map(config => config.systemType);
        const requiredSystems = [
          SystemType.WATER,
          SystemType.ELECTRICITY,
          SystemType.SECURITY,
          SystemType.INTERNET,
          SystemType.MAINTENANCE,
        ];

        const missingSystems = requiredSystems.filter(
          systemType => !existingSystemTypes.includes(systemType)
        );

        if (missingSystems.length > 0) {
          console.log(`   ➕ Adding missing systems: ${missingSystems.join(', ')}`);

          const missingConfigs = missingSystems.map((systemType, index) => ({
            propertyId: property.id,
            systemType,
            displayName: getSystemDisplayName(systemType),
            displayOrder: existingConfigs.length + index + 1,
            isEnabled: true,
            configuration: {},
          }));

          const createdConfigs = await prisma.propertySystemConfig.createMany({
            data: missingConfigs,
          });

          console.log(`   ✅ Added ${createdConfigs.count} missing system configurations`);
        } else {
          console.log(`   ✅ All required systems already exist`);
        }
      }
    }

    console.log('\n🎉 Property system configuration check completed successfully!');
  } catch (error) {
    console.error('❌ Error ensuring property system configs:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

function getSystemDisplayName(systemType: SystemType): string {
  switch (systemType) {
    case SystemType.WATER:
      return 'Water Management';
    case SystemType.ELECTRICITY:
      return 'Electricity Management';
    case SystemType.SECURITY:
      return 'Security Management';
    case SystemType.INTERNET:
      return 'Internet Management';
    case SystemType.MAINTENANCE:
      return 'Maintenance Management';
    case SystemType.OTT:
      return 'OTT Services';
    default:
      return systemType;
  }
}

// Run the script if called directly
if (require.main === module) {
  ensurePropertySystemConfigs()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

export { ensurePropertySystemConfigs };

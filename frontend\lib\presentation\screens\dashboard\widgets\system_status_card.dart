import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../data/models/dashboard.dart';
import '../../../providers/auth_providers.dart';
import '../../../routes/app_router.dart';

class SystemStatusCard extends ConsumerWidget {
  final List<SystemStatusOverview> systemStatuses;
  final Map<String, dynamic> stats;

  const SystemStatusCard({
    super.key,
    required this.systemStatuses,
    required this.stats,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.monitor_heart,
                  size: 24,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'System Status Overview',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildStatusSummary(context),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // System Status Grid - Responsive
            LayoutBuilder(
              builder: (context, constraints) {
                final crossAxisCount = constraints.maxWidth > 900 ? 3 :
                                     constraints.maxWidth > 600 ? 2 : 1;
                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    childAspectRatio: 1.2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: systemStatuses.length,
                  itemBuilder: (context, index) {
                    final systemStatus = systemStatuses[index];
                    return _buildSystemStatusItem(context, ref, systemStatus);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSummary(BuildContext context) {
    final operationalSystems = stats['operationalSystems'] as int;
    final totalSystems = stats['totalSystems'] as int;
    final healthPercentage = totalSystems > 0 
        ? (operationalSystems / totalSystems) * 100 
        : 0.0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getHealthColor(healthPercentage).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getHealthColor(healthPercentage).withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        '$operationalSystems/$totalSystems Operational',
        style: TextStyle(
          color: _getHealthColor(healthPercentage),
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildSystemStatusItem(BuildContext context, WidgetRef ref, SystemStatusOverview systemStatus) {
    final systemIcon = _getSystemIcon(systemStatus.systemType);
    final systemColor = _getSystemColor(systemStatus.systemType);
    final overallStatus = _getOverallStatus(systemStatus);
    final statusColor = _getStatusColor(overallStatus);

    return ConstrainedBox(
      constraints: const BoxConstraints(
        minWidth: 180,
        minHeight: 140,
      ),
      child: InkWell(
        onTap: () => _navigateToSystemScreen(context, ref, systemStatus.systemType),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: statusColor.withValues(alpha: 0.2),
            ),
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // System Icon and Status
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: systemColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  systemIcon,
                  size: 28,
                  color: systemColor,
                ),
              ),
              const Spacer(),
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: statusColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // System Name
          Text(
            _getSystemDisplayName(systemStatus.systemType),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 4),
          
          // Status Text
          Text(
            overallStatus,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Status Breakdown
          if (systemStatus.total > 0) ...[
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildStatusDot(AppTheme.successColor, systemStatus.operational),
                _buildStatusDot(AppTheme.warningColor, systemStatus.warning),
                _buildStatusDot(AppTheme.errorColor, systemStatus.critical),
                _buildStatusDot(Colors.grey, systemStatus.offline),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '${systemStatus.total} total',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ] else ...[
            Text(
              'No systems',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
        ),
      ),
    );
  }

  Widget _buildStatusDot(Color color, int count) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  IconData _getSystemIcon(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'ELECTRICITY':
        return Icons.electrical_services;
      case 'WATER':
        return Icons.water_drop;
      case 'SECURITY':
        return Icons.security;
      case 'INTERNET':
        return Icons.wifi;
      case 'OTT':
        return Icons.tv;
      case 'MAINTENANCE':
        return Icons.build;
      default:
        return Icons.settings;
    }
  }

  Color _getSystemColor(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'ELECTRICITY':
        return Colors.amber;
      case 'WATER':
        return Colors.blue;
      case 'SECURITY':
        return Colors.red;
      case 'INTERNET':
        return Colors.green;
      case 'OTT':
        return Colors.purple;
      case 'MAINTENANCE':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getSystemDisplayName(String systemType) {
    switch (systemType.toUpperCase()) {
      case 'ELECTRICITY':
        return 'Electricity';
      case 'WATER':
        return 'Water';
      case 'SECURITY':
        return 'Security';
      case 'INTERNET':
        return 'Internet';
      case 'OTT':
        return 'OTT Services';
      case 'MAINTENANCE':
        return 'Maintenance';
      default:
        return systemType;
    }
  }

  String _getOverallStatus(SystemStatusOverview systemStatus) {
    if (systemStatus.total == 0) return 'No Data';
    if (systemStatus.critical > 0) return 'Critical';
    if (systemStatus.warning > 0) return 'Warning';
    if (systemStatus.offline > 0) return 'Partial';
    return 'Operational';
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'OPERATIONAL':
        return AppTheme.successColor;
      case 'WARNING':
      case 'PARTIAL':
        return AppTheme.warningColor;
      case 'CRITICAL':
        return AppTheme.errorColor;
      case 'NO DATA':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  Color _getHealthColor(double percentage) {
    if (percentage >= 90) return AppTheme.successColor;
    if (percentage >= 75) return AppTheme.infoColor;
    if (percentage >= 50) return AppTheme.warningColor;
    return AppTheme.errorColor;
  }

  void _navigateToSystemScreen(BuildContext context, WidgetRef ref, String systemType) {
    final userPermissions = ref.read(userPermissionsProvider);

    // Get user's assigned properties
    if (userPermissions.assignedPropertyIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No properties assigned. Please contact your administrator.'),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // Use the first assigned property (or show property selector for multiple properties)
    final propertyId = userPermissions.assignedPropertyIds.first;

    switch (systemType.toUpperCase()) {
      case 'ELECTRICITY':
        context.go('/properties/$propertyId/electricity');
        break;
      case 'WATER':
        context.go('/properties/$propertyId/water');
        break;
      case 'SECURITY':
        context.go('/properties/$propertyId/security');
        break;
      case 'MAINTENANCE':
        context.go('/properties/$propertyId/maintenance');
        break;
      case 'OTT':
        context.go('/properties/$propertyId/ott');
        break;
      case 'INTERNET':
        context.go('/properties/$propertyId/internet');
        break;
      default:
        // For unknown system types, show a snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$systemType management screen coming soon!'),
            duration: const Duration(seconds: 2),
          ),
        );
    }
  }
}

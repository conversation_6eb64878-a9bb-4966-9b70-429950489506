import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/maintenance.dart';
import '../../data/repositories/maintenance_repository.dart';
import '../../core/services/service_locator.dart';

// Maintenance repository provider
final maintenanceRepositoryProvider = Provider<MaintenanceRepository>((ref) {
  return serviceLocator.maintenanceRepository;
});

// API-driven maintenance functions provider
final maintenanceFunctionsProvider = FutureProvider<List<MaintenanceFunction>>((ref) async {
  final repository = ref.read(maintenanceRepositoryProvider);

  try {
    final response = await repository.getMaintenanceFunctions();
    if (response.success && response.data != null) {
      return response.data!;
    }
    throw Exception(response.message ?? 'Failed to load maintenance functions');
  } catch (e) {
    throw Exception('Error loading maintenance functions: $e');
  }
});

// Maintenance functions by department provider
final maintenanceFunctionsByDepartmentProvider = Provider<Map<String, List<MaintenanceFunction>>>((ref) {
  final functionsAsync = ref.watch(maintenanceFunctionsProvider);

  return functionsAsync.when(
    data: (functions) {
      final Map<String, List<MaintenanceFunction>> grouped = {};
      for (final function in functions) {
        grouped.putIfAbsent(function.department, () => []).add(function);
      }
      return grouped;
    },
    loading: () => {},
    error: (_, __) => {},
  );
});

// Maintenance function search provider
final maintenanceFunctionSearchProvider = StateProvider<String>((ref) => '');

// Filtered maintenance functions provider
final filteredMaintenanceFunctionsProvider = Provider<AsyncValue<List<MaintenanceFunction>>>((ref) {
  final functionsAsync = ref.watch(maintenanceFunctionsProvider);
  final searchQuery = ref.watch(maintenanceFunctionSearchProvider);

  return functionsAsync.when(
    data: (functions) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(functions);
      }

      final filtered = functions.where((function) {
        return function.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
               function.department.toLowerCase().contains(searchQuery.toLowerCase()) ||
               function.subFunction.toLowerCase().contains(searchQuery.toLowerCase()) ||
               function.responsibleAgent.toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();

      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});
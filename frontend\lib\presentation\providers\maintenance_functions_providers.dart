import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/maintenance.dart';

// For now, we'll use static data but this will be replaced with API calls
final maintenanceFunctionsProvider = FutureProvider<List<MaintenanceFunction>>((ref) async {
  // Simulate API call delay
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Return static data for now - this will be replaced with actual API integration
  return const [
    MaintenanceFunction(
      name: 'Attendance - Office',
      subFunction: 'Staff Presence',
      department: 'Maintenance',
      input: 'Check-in/check-out logs',
      process: 'Compare vs shift schedule',
      output: 'Present/Absent',
      thresholdLimits: 'Max 1 absence/week',
      responsibleAgent: 'Office Incharge',
    ),
    MaintenanceFunction(
      name: 'CCTV',
      subFunction: 'Feed Uptime',
      department: 'CCTV',
      input: 'IP stream status',
      process: 'Ping every hour',
      output: 'Online/Offline',
      thresholdLimits: 'Max downtime: 2 hrs',
      responsibleAgent: 'CCTV Vendor',
    ),
    MaintenanceFunction(
      name: 'Water Supply',
      subFunction: 'Tank Level',
      department: 'Water',
      input: 'Level sensor reading',
      process: 'Check against minimum threshold',
      output: 'Normal/Low/Critical',
      thresholdLimits: 'Min: 20%, Critical: 10%',
      responsibleAgent: 'Water Maintenance Team',
    ),
    MaintenanceFunction(
      name: 'Electricity',
      subFunction: 'Power Consumption',
      department: 'Electrical',
      input: 'Meter readings',
      process: 'Calculate daily usage',
      output: 'kWh consumed',
      thresholdLimits: 'Alert if >150% of avg',
      responsibleAgent: 'Electrical Team',
    ),
    MaintenanceFunction(
      name: 'Security',
      subFunction: 'Guard Patrol',
      department: 'Security',
      input: 'Checkpoint scans',
      process: 'Verify patrol schedule',
      output: 'Completed/Missed',
      thresholdLimits: 'Max 1 missed patrol/shift',
      responsibleAgent: 'Security Supervisor',
    ),
    MaintenanceFunction(
      name: 'Housekeeping',
      subFunction: 'Common Area Cleaning',
      department: 'Housekeeping',
      input: 'Cleaning checklist',
      process: 'Daily inspection',
      output: 'Clean/Needs Attention',
      thresholdLimits: 'All areas daily',
      responsibleAgent: 'Housekeeping Supervisor',
    ),
    MaintenanceFunction(
      name: 'Lift Maintenance',
      subFunction: 'Operational Status',
      department: 'Mechanical',
      input: 'Lift status sensors',
      process: 'Monitor operation',
      output: 'Working/Under Maintenance',
      thresholdLimits: 'Max downtime: 4 hrs',
      responsibleAgent: 'Lift Technician',
    ),
    MaintenanceFunction(
      name: 'Fire Safety',
      subFunction: 'System Check',
      department: 'Safety',
      input: 'Alarm system status',
      process: 'Weekly system test',
      output: 'Functional/Needs Service',
      thresholdLimits: 'Monthly full test',
      responsibleAgent: 'Fire Safety Officer',
    ),
    MaintenanceFunction(
      name: 'Waste Management',
      subFunction: 'Collection Schedule',
      department: 'Sanitation',
      input: 'Collection logs',
      process: 'Track pickup times',
      output: 'On Time/Delayed',
      thresholdLimits: 'Daily collection',
      responsibleAgent: 'Sanitation Team',
    ),
    MaintenanceFunction(
      name: 'Parking Management',
      subFunction: 'Space Allocation',
      department: 'Parking',
      input: 'Vehicle entry/exit logs',
      process: 'Monitor occupancy',
      output: 'Available/Full',
      thresholdLimits: 'Max 95% occupancy',
      responsibleAgent: 'Parking Attendant',
    ),
    MaintenanceFunction(
      name: 'Internet Connectivity',
      subFunction: 'Network Uptime',
      department: 'IT',
      input: 'Network monitoring',
      process: 'Ping test every 5 min',
      output: 'Online/Offline',
      thresholdLimits: 'Min 99% uptime',
      responsibleAgent: 'IT Support',
    ),
    MaintenanceFunction(
      name: 'Intercom System',
      subFunction: 'Communication Check',
      department: 'Communication',
      input: 'System status',
      process: 'Daily functionality test',
      output: 'Working/Faulty',
      thresholdLimits: 'All units functional',
      responsibleAgent: 'Communication Tech',
    ),
    MaintenanceFunction(
      name: 'Garden Maintenance',
      subFunction: 'Landscaping',
      department: 'Landscaping',
      input: 'Visual inspection',
      process: 'Weekly maintenance check',
      output: 'Well Maintained/Needs Work',
      thresholdLimits: 'Weekly trimming',
      responsibleAgent: 'Gardener',
    ),
    MaintenanceFunction(
      name: 'Swimming Pool',
      subFunction: 'Water Quality',
      department: 'Recreation',
      input: 'Chemical test results',
      process: 'Daily water testing',
      output: 'Safe/Unsafe',
      thresholdLimits: 'pH: 7.2-7.6, Chlorine: 1-3ppm',
      responsibleAgent: 'Pool Maintenance',
    ),
    MaintenanceFunction(
      name: 'Gym Equipment',
      subFunction: 'Equipment Status',
      department: 'Recreation',
      input: 'Equipment inspection',
      process: 'Weekly safety check',
      output: 'Safe/Needs Repair',
      thresholdLimits: 'All equipment functional',
      responsibleAgent: 'Gym Attendant',
    ),
    MaintenanceFunction(
      name: 'Club House',
      subFunction: 'Facility Booking',
      department: 'Recreation',
      input: 'Booking requests',
      process: 'Schedule management',
      output: 'Available/Booked',
      thresholdLimits: 'Max 80% utilization',
      responsibleAgent: 'Club House Manager',
    ),
    MaintenanceFunction(
      name: 'Visitor Management',
      subFunction: 'Entry Tracking',
      department: 'Security',
      input: 'Visitor logs',
      process: 'Verify with residents',
      output: 'Authorized/Unauthorized',
      thresholdLimits: 'All visitors logged',
      responsibleAgent: 'Security Guard',
    ),
    MaintenanceFunction(
      name: 'Complaint Management',
      subFunction: 'Issue Resolution',
      department: 'Administration',
      input: 'Resident complaints',
      process: 'Track resolution time',
      output: 'Resolved/Pending',
      thresholdLimits: 'Max 48 hrs resolution',
      responsibleAgent: 'Admin Officer',
    ),
    MaintenanceFunction(
      name: 'Maintenance Requests',
      subFunction: 'Work Order Processing',
      department: 'Maintenance',
      input: 'Service requests',
      process: 'Assign and track',
      output: 'Completed/In Progress',
      thresholdLimits: 'Max 24 hrs response',
      responsibleAgent: 'Maintenance Supervisor',
    ),
    MaintenanceFunction(
      name: 'Emergency Response',
      subFunction: 'Incident Management',
      department: 'Emergency',
      input: 'Emergency alerts',
      process: 'Response protocol',
      output: 'Resolved/Escalated',
      thresholdLimits: 'Max 15 min response',
      responsibleAgent: 'Emergency Coordinator',
    ),
  ];
});

// Maintenance functions by department provider
final maintenanceFunctionsByDepartmentProvider = Provider<Map<String, List<MaintenanceFunction>>>((ref) {
  final functionsAsync = ref.watch(maintenanceFunctionsProvider);
  
  return functionsAsync.when(
    data: (functions) {
      final Map<String, List<MaintenanceFunction>> grouped = {};
      for (final function in functions) {
        grouped.putIfAbsent(function.department, () => []).add(function);
      }
      return grouped;
    },
    loading: () => {},
    error: (_, __) => {},
  );
});

// Maintenance function search provider
final maintenanceFunctionSearchProvider = StateProvider<String>((ref) => '');

// Filtered maintenance functions provider
final filteredMaintenanceFunctionsProvider = Provider<AsyncValue<List<MaintenanceFunction>>>((ref) {
  final functionsAsync = ref.watch(maintenanceFunctionsProvider);
  final searchQuery = ref.watch(maintenanceFunctionSearchProvider);
  
  return functionsAsync.when(
    data: (functions) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(functions);
      }
      
      final filtered = functions.where((function) {
        return function.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
               function.department.toLowerCase().contains(searchQuery.toLowerCase()) ||
               function.subFunction.toLowerCase().contains(searchQuery.toLowerCase()) ||
               function.responsibleAgent.toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});
